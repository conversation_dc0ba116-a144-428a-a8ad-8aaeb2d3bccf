package com.wnkx.order.controller;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.enums.AuditStatusEnum;
import com.ruoyi.common.core.enums.OrderTypeEnum;
import com.ruoyi.common.security.annotation.InnerAuth;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.logistic.LogisticFollowNotifyDTO;
import com.ruoyi.system.api.domain.dto.order.logistic.ModelUpdateAddressDTO;
import com.ruoyi.system.api.domain.dto.order.pay.OrderPayAccountDTO;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo;
import com.ruoyi.system.api.domain.entity.order.Order;
import com.ruoyi.system.api.domain.entity.order.OrderMember;
import com.ruoyi.system.api.domain.entity.order.OrderPayeeAccount;
import com.ruoyi.system.api.domain.entity.order.promotion.PromotionActivityAmendmentRecord;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.ModelBasicDataVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.order.ModelOrderVO;
import com.ruoyi.system.api.domain.vo.order.OrderModelTimeoutVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayDetailVO;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.AsyncTaskService;
import com.wnkx.order.service.core.OrderVideoLogisticCore;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2025/2/27 10:08
 */
@RestController
@RequestMapping("/order/inner")
@Api(value = "订单内部调用服务", tags = "订单内部调用服务")
@RequiredArgsConstructor
@Validated
public class OrderInnerController {

    private final IOrderService orderService;
    private final AsyncTaskService asyncTaskService;
    private final IOrderVideoService orderVideoService;
    private final IOrderMemberService orderMemberService;
    private final OrderVideoMatchService orderVideoMatchService;
    private final OrderVideoLogisticCore orderVideoLogisticCore;
    private final PromotionActivityAmendmentRecordService promotionActivityAmendmentRecordService;

    /**
     * 查询有逾期未反馈素材和无法接单的模特
     *
     * @return 模特id
     */
    @PostMapping("/query-order-model")
    @InnerAuth
    public R<List<Long>> checkAbnormalModelId(@RequestBody Collection<Long> modelId) {
        return R.ok(orderService.checkAbnormalModelId(modelId));
    }


    /**
     * 新增活动修改记录
     *
     * @return 模特id
     */
    @PostMapping("/promotionActivity/savePromotionActivityAmendmentRecord")
    @InnerAuth
    public R<String> savePromotionActivityAmendmentRecord(@RequestBody PromotionActivityAmendmentRecord promotionActivityAmendmentRecord) {
        promotionActivityAmendmentRecordService.savePromotionActivityAmendmentRecord(promotionActivityAmendmentRecord);
        return R.ok("保存成功");
    }


    /**
     * 获取会员有效订单数量
     *
     * @param bizUserId
     * @return
     */
    @GetMapping("/getValidOrderCount")
    @InnerAuth
    public R<Long> getValidOrderMemberCount(@RequestParam Long bizUserId) {
        return R.ok(orderService.getValidOrderMemberCount(bizUserId));
    }


    /**
     * 获取会员有效订单
     */
    @GetMapping("/get-valid-order-list")
    @InnerAuth
    public R<List<OrderMember>> getValidOrderMemberList(@RequestParam Long bizUserId) {
        return R.ok(orderMemberService.getValidOrderMemberList(bizUserId));
    }


    /**
     * 获取未取消订单数量*
     * @param businessId
     * @return
     */
    @GetMapping("/getUnCancelOrderCount")
    @InnerAuth
    public R<Long> getUnCancelOrderCount(@RequestParam Long businessId) {
        return R.ok(orderService.getUnCancelOrderCount(businessId));
    }

    /**
     * 批量更新视频订单的对接人
     */
    @PostMapping("/update-order-contact")
    @InnerAuth
    public R<Boolean> updateOrderVideoContact(@RequestBody UpdateOrderContactDTO dto) {
        Boolean result = orderService.updateOrderVideoContact(dto);
        return R.ok(result);
    }

    /**
     * 接收抓取亚马逊图片更新视频订单
     */
    @PostMapping("/update-batch-order-video-product-pic")
    @InnerAuth
    public R<String> updateBatchOrderVideoProductPic(@RequestBody List<UpdateBatchOrderVideoProductPicDTO> dto) {
        orderService.updateBatchOrderVideoProductPic(dto);
        return R.ok();
    }

    /**
     * 接收抓取亚马逊图片更新购物车订单
     */
    @PostMapping("/update-batch-order-cart-product-pic")
    @InnerAuth
    public R<String> updateBatchOrderCartProductPic(@RequestBody List<UpdateBatchOrderVideoProductPicDTO> dto) {
        orderService.updateBatchOrderCartProductPic(dto);
        return R.ok();
    }

    /**
     * 获取模特待拍数、已完成订单数、超时订单
     */
    @PostMapping("/get-model-order-count")
    @InnerAuth
    public R<List<ModelOrderVO>> getModelOrderCount(@RequestBody Collection<Long> modelIds) {
        List<ModelOrderVO> modelOrderVOS = orderService.getModelOrderCount(modelIds);
        return R.ok(modelOrderVOS);
    }

    /**
     * 获取模特超时率、售后率
     */
    @GetMapping("/get-model-overtime-rate-and-after-sale-rate")
    @InnerAuth
    public R<List<OrderModelTimeoutVO>> getModelOvertimeRateAndAfterSaleRate() {
        List<OrderModelTimeoutVO> orderModelTimeoutVOS = orderService.getModelOvertimeRateAndAfterSaleRate();
        return R.ok(orderModelTimeoutVOS);
    }

    @PostMapping("/getBasePayDetailVOS")
    @InnerAuth
    public R<List<OrderPayDetailVO>> getBasePayDetailVOS(@RequestBody OrderPayDetailDTO dto) {
        return R.ok(orderService.getBasePayDetailVOS(dto));
    }

    /**
     * 获取模特剩余可携带订单数
     */
    @PostMapping("/saveOrderPayLog")
    @InnerAuth
    public R<String> saveOrderPayLog(@RequestBody @Validated OrderPayLogDTO dto) {
        orderService.saveOrderPayLog(dto);
        return R.ok("成功");
    }

    /**
     * 禁用会员订单发票
     * * @param businessId
     * @return
     */
    @PostMapping("/banMemberInvoice")
    @InnerAuth
    public R<String> banMemberInvoice(@RequestParam Long businessId) {
        orderService.banMemberInvoice(businessId);
        return R.ok("成功");
    }

    @PostMapping("/saveOrderPayeeAccount")
    @InnerAuth
    public R<String> saveOrderPayeeAccount(@RequestBody @Validated OrderPayAccountDTO dto) {
        orderService.saveOrderPayeeAccount(dto);
        return R.ok("成功");
    }

    @PostMapping("/queryOrderPayeeAccountListByOrderNums")
    @InnerAuth
    public R<List<OrderPayeeAccount>> queryOrderPayeeAccountListByOrderNums(@RequestBody Collection<String> orderNums) {
        return R.ok(orderService.queryOrderPayeeAccountListByOrderNums(new ArrayList<>(orderNums)));
    }

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    @PostMapping("/update-issue-id")
    @InnerAuth
    public R<String> updateIssueId(@RequestBody UpdateIssueIdDTO dto) {
        orderService.updateIssueId(dto);
        return R.ok();
    }

    /**
     * 清楚购物车意向模特
     * @param dto
     * @return
     */
    @PostMapping("/clearVideoCartIntentionModelId")
    @InnerAuth
    public R<String> clearVideoCartIntentionModelId(@RequestBody @Validated ClearVideoCartIntentionModelDTO dto) {
        orderService.clearVideoCartIntentionModelId(dto);
        return R.ok();
    }

    /**
     * 获取已支付的视频订单
     */
    @GetMapping("/getPayedOrderList")
    @InnerAuth
    public R<List<Order>> getPayedOrderList() {
        return R.ok(orderService.lambdaQuery()
                .eq(Order::getOrderType, OrderTypeEnum.VIDEO_ORDER.getCode())
                .isNotNull(Order::getRecordTime)
                .list());
    }

    @GetMapping("/getPayedUnCheckOrderList")
    @InnerAuth
    public R<List<Order>> getPayedUnCheckOrderList() {
        return R.ok(orderService.lambdaQuery()
                .eq(Order::getAuditStatus, AuditStatusEnum.UN_CHECK.getCode())
                .isNotNull(Order::getSubmitCredentialTime)
                .list());
    }

    @PostMapping("/getOrderListByOrderNums")
    @InnerAuth
    public R<List<Order>> getPayedUnCheckOrderList(@RequestBody List<String> orderNums) {
        return R.ok(orderService.lambdaQuery()
                .in(Order::getOrderNum, orderNums)
                .list());
    }

    @PostMapping("/closeAllOrder")
    @InnerAuth
    public R<String> closeAllOrder(@RequestBody List<String> orderNums) {
        asyncTaskService.closeAllOrder(orderNums);
        return R.ok("关闭成功");
    }

    /**
     * 通过商家ID查询商家是否有进行中或者交易完成的视频订单
     */
    @GetMapping("/has-valid-video-order-by-business-id")
    @InnerAuth
    public R<Boolean> hasValidVideoOrderByBusinessId(@RequestParam Long businessId, @RequestParam(required = false) String startTime) {
        return R.ok(orderVideoService.hasValidVideoOrderByBusinessId(businessId, startTime));
    }

    /**
     * 模特数据统计-获取模特基础数据
     */
    @GetMapping("/get-model-basic-data")
    @InnerAuth
    public R<ModelBasicDataVO> getModelBasicData() {
        return R.ok(orderVideoMatchService.getModelBasicData());
    }

    /**
     * 模特数据统计-模特接单排行榜
     */
    @GetMapping("/get-model-order-ranking")
    @InnerAuth
    public R<List<ModelOrderRankingInfo>> getModelOrderRanking(@RequestParam String date) {
        return R.ok(orderVideoService.getModelOrderRanking(date));
    }


    /**
     * （一次性）初始化模特数据-模特接单排行榜
     */
    @PostMapping("/get-model-order-rankings")
    @InnerAuth
    public R<Map<String, List<ModelOrderRankingInfo>>> getModelOrderRankings(@RequestBody List<String> collect) {
        return R.ok(orderVideoService.getModelOrderRankings(collect));
    }

    @PostMapping("/logisticFollowNotify")
    @InnerAuth
    public R<String> logisticFollowNotify(@RequestBody @Validated LogisticFollowNotifyDTO dto) {
        orderVideoLogisticCore.logisticFollowNotify(dto);
        return R.ok("通知成功");
    }
    @PostMapping("/modelUpdateAddress")
    @InnerAuth
    public R<String> modelUpdateAddress(@RequestBody @Validated ModelUpdateAddressDTO dto) {
        orderVideoLogisticCore.modelUpdateAddress(dto);
        return R.ok("通知成功");
    }

    /**
     * 模特数据-模特排单情况
     */
    @PostMapping("/model-order-scheduled-data")
    @InnerAuth
    public R<List<PieChartVO>> getModelOrderScheduledData(@RequestBody Collection<Long> modelIds) {
        List<PieChartVO> pieChartVOS = orderVideoService.getModelOrderScheduledData(modelIds);
        return R.ok(pieChartVOS);
    }


    /**
     * 获取订单第一次进入待匹配时间
     */
    @PostMapping("/getOrderFirstMatchTime")
    @InnerAuth
    public R<Date> getOrderFirstMatchTime(@RequestBody Long orderId){
        Date firstMatchTime = orderVideoService.getOrderFirstMatchTime(orderId);
        return R.ok(firstMatchTime);
    }

    /**
     * 查询模特数据表列表
     */
    @PostMapping("/model-data-table/list")
    @InnerAuth
    public R<List<ModelDataTableListVO>> selectModelDataTableListByCondition(@RequestBody ModelDataTableListDTO modelDataTableListDTO) throws ExecutionException, InterruptedException {
        List<ModelDataTableListVO> modelDataTableListVO = orderVideoMatchService.selectModelDataTableListByCondition(modelDataTableListDTO);
        return R.ok(modelDataTableListVO);
    }

    /**
     * 获取同链接产品有回退订单的历史拍摄模特ID
     */
    @GetMapping("/select-same-product-rollback-model-ids-by-video-id")
    @InnerAuth
    public R<List<Long>> selectSameProductRollbackModelIdsByVideoId(@RequestParam Long videoId) {
        List<Long> modelIds = orderVideoService.selectSameProductRollbackModelIdsByVideoId(videoId);
        return R.ok(modelIds);
    }
}
