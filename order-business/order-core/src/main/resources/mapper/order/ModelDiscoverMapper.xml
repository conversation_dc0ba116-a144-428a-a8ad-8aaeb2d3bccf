<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wnkx.order.mapper.ModelDiscoverMapper">

    <sql id="recommendListSql">
        FROM
            order_video_match_preselect_model ovmpm
                JOIN order_video_match ovm ON ovm.id = ovmpm.match_id
                JOIN order_video ov ON ov.id = ovm.video_id AND ov.rollback_id &lt;=&gt; ovm.rollback_id
        WHERE
            ovm.`status` = ${@<EMAIL>}
        AND ov.`status` = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode}
        AND ovmpm.add_type = ${@<EMAIL>}
        AND ovmpm.model_intention = ${@com.ruoyi.common.core.enums.ModelIntentionEnum@MT_UN_CONFIRM.getCode}
        AND ovmpm.model_id = #{modelId}
        AND ovmpm.distribution_result = ${@<EMAIL>}
        AND ovmpm.status NOT IN (${@<EMAIL>},${@<EMAIL>})
        AND NOT EXISTS (
            SELECT
                1
            FROM
                order_video_match_preselect_model ovmpm_sub
            WHERE
                ovmpm_sub.match_id = ovmpm.match_id
                AND ( ovmpm_sub.STATUS = ${@<EMAIL>}
                    OR (
                    ovmpm_sub.model_id = #{modelId}
                    AND ovmpm_sub.model_intention != ${@com.ruoyi.common.core.enums.ModelIntentionEnum@MT_UN_CONFIRM.getCode}
                    AND ovmpm_sub.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                    AND ovmpm_sub.status != ${@<EMAIL>}
                    )
                )
        )
    </sql>

    <sql id="selectOrderQualityModelListSql">
        FROM
            order_video ov
                JOIN order_video_match ovm ON ovm.video_id = ov.id AND COALESCE ( ovm.rollback_id, 0 ) = COALESCE ( ov.rollback_id, 0 )
                                                                    <!--已暂停的匹配单不展示-->
                                                                    AND ovm.`status` = ${@<EMAIL>}
                                                                    AND ovm.end_time IS NULL
        <where>
            <include refid="OrderModelListConditionFilter"/>

            <if test="dto.keyword != null and dto.keyword != ''">
                AND  ov.product_english like concat('%', #{dto.keyword}, '%')
            </if>

            <if test="dto.specialtyCategory != null and dto.specialtyCategory.size() != 0">
                AND  ov.id in (SELECT DISTINCT(ovt.video_id) FROM order_video_tag ovt WHERE ovt.tag_id IN
                <foreach collection="dto.specialtyCategory" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
                AND ovt.category_id = ${@<EMAIL>}
                )
            </if>
        </where>
        order by ovm.shuffled_sort_key
    </sql>
    <sql id="OrderModelListConditionFilter">
        ov.STATUS = ${@com.ruoyi.common.core.enums.OrderStatusEnum@UN_MATCH.getCode}
        AND ov.release_time &lt;= NOW()

        <!--视频订单满足模特基本要求（国家、模特类型、平台）-->
        AND ov.shooting_country = #{dto.shootingCountry}
        AND ( ov.model_type = #{dto.modelType} OR ov.model_type = ${@<EMAIL>} )
        AND ov.platform IN
        <foreach collection="dto.platform" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND NOT EXISTS(
            SELECT 1
            FROM order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm on ovmpm.match_id = ovm.id
            WHERE
                ovm.video_id = ov.id
                AND (
                    (
                        COALESCE(ovm.rollback_id, 0) = COALESCE(ov.rollback_id, 0)
                        <!--视频订单的预选模特列表不能有[已选定]的模特-->
                        AND (
                            ovmpm.STATUS = ${@<EMAIL>}
                            OR (
                                ovmpm.add_type = ${@com.ruoyi.common.core.enums.PreselectModelAddTypeEnum@INTENTION_MODEL.getCode}
                                AND ovmpm.status != ${@<EMAIL>}
                                AND ovmpm.add_time = ovm.start_time
                                )
                            )
                    )
                    <!--视频订单的预选模特列表不能有当前模特且非模特主动取消申请且非取消分发-->
                    OR  (
                        ovmpm.model_id = #{dto.curModelId}
                        AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                        AND (
                            ovmpm.add_type != ${@<EMAIL>}
                            OR ovmpm.distribution_result NOT IN ( ${@com.ruoyi.common.core.enums.DistributionResultEnum@WANT_NOT.getCode},${@com.ruoyi.common.core.enums.DistributionResultEnum@CANCEL_DISTRIBUTION.getCode} )
                        )
                    )
                )
        )

        <!--未淘汰预选模特数达到或超过设定的坑位数时 不展示-->
        AND NOT EXISTS (
            SELECT 1
            FROM order_video_match ovm
                JOIN order_video_match_preselect_model ovmpm ON ovmpm.match_id = ovm.id
            WHERE
                ovm.video_id = ov.id
                AND COALESCE(ovm.rollback_id, 0) = COALESCE(ov.rollback_id, 0)
            GROUP BY
                ovm.video_id
            HAVING
                COUNT(CASE WHEN ovmpm.STATUS != ${@<EMAIL>}
                                AND ovmpm.select_status != ${@com.ruoyi.common.core.enums.OrderVideoModelSelectStatusEnum@CANCEL_APPLY.getCode}
                                AND (
                                    ovmpm.add_type != ${@<EMAIL>}
                                    OR ovmpm.distribution_result = ${@<EMAIL>}
                                )
                 THEN 1 END) >= #{dto.preselectModelNumberOfPits}
        )

        <if test="dto.videoId != null ">
            AND ov.id = #{dto.videoId}
        </if>

        <if test="dto.blackModelBusinessIds != null and dto.blackModelBusinessIds.size() > 0 ">
            AND ov.create_order_business_id NOT IN
            <foreach collection="dto.blackModelBusinessIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!--模特端-首页-订单列表-->
    <select id="selectOrderQualityModelListByCondition" resultType="com.ruoyi.system.api.domain.vo.order.OrderModelListVO">
        SELECT
            ov.id,
            ov.product_pic,
            ov.video_code,
            ov.product_english,
            ov.platform,
            ov.video_duration,
            ov.video_style,
            ov.pic_count,
            ov.refund_pic_count,
            ov.create_order_biz_user_id
        <include refid="selectOrderQualityModelListSql"/>
    </select>
    
    <select id="getOrderQualityModelCountByCondition" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            order_video ov
                JOIN order_video_match ovm ON ovm.video_id = ov.id
                    AND COALESCE ( ovm.rollback_id, 0 ) = COALESCE ( ov.rollback_id, 0 )
                    <!--已暂停的匹配单不展示-->
                    AND ovm.`status` = ${@<EMAIL>}
        <where>
            <include refid="OrderModelListConditionFilter"/>
        </where>
    </select>
    <select id="selectRecommendList" resultType="com.ruoyi.system.api.domain.vo.order.RecommendModelListVO">
        SELECT
            ov.id AS video_id,
            ovmpm.id AS preselectModelId,
            ov.product_pic,
            ov.video_code,
            ov.product_english,
            ov.video_style,
            ov.video_duration,
            ov.pic_count,
            ov.refund_pic_count
        <include refid="recommendListSql"/>
    </select>
    <select id="getRecommendCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        <include refid="recommendListSql"/>
    </select>
    <sql id="selectOrderOrdinaryModelListSql">
        WITH
        base_ids AS (
            SELECT
                ov.id
            <include refid="selectOrderQualityModelListSql"/>
        ),

        latest_match AS (
        SELECT
        om.match_id,
        om.video_id,
        om.start_time,
        om.shuffled_sort_key
        FROM (
        SELECT
        ovm.id         AS match_id,
        ovm.video_id,
        ovm.start_time,
        ovm.shuffled_sort_key,
        ROW_NUMBER() OVER (
        PARTITION BY ovm.video_id
        ORDER BY ovm.start_time DESC
        ) AS rn
        FROM order_video_match ovm
        WHERE
        ovm.status      = 1
        AND ovm.end_time IS NULL
        AND ovm.video_id IN (SELECT id FROM base_ids)
        ) om
        WHERE om.rn = 1
        ),

        first_ps AS (
        SELECT
        pm.match_id,
        pm.add_type,
        pm.add_time,
        pm.oust_time,
        lm.video_id
        FROM order_video_match_preselect_model pm
        JOIN latest_match lm
        ON pm.match_id = lm.match_id
        WHERE pm.add_type = 1
        AND pm.add_time = lm.start_time
        ),

        events_all AS (
        SELECT
        lm.video_id,
        lm.start_time AS t,
        0 AS delta
        FROM base_ids b
        JOIN latest_match lm ON b.id = lm.video_id

        UNION ALL

        SELECT
        ovm.video_id,
        (CASE WHEN  ovmpm.add_type =4 THEN ovmpm.distribution_result_time ELSE ovmpm.add_time END ) AS t,
        +1 AS delta
        FROM order_video_match_preselect_model ovmpm
        JOIN order_video_match ovm
        ON ovmpm.match_id = ovm.id
        JOIN latest_match lm
        ON ovm.id = lm.match_id
        WHERE ovmpm.add_type != 4
        OR ovmpm.distribution_result = 2

        UNION ALL

        SELECT
        ovm.video_id,
        (CASE WHEN  ovmpm.select_status =6 THEN ovmpm.select_time ELSE ovmpm.oust_time END ) AS t,
        -1 AS delta
        FROM order_video_match_preselect_model ovmpm
        JOIN order_video_match ovm
        ON ovmpm.match_id = ovm.id
        JOIN latest_match lm
        ON ovm.id = lm.match_id
        WHERE (ovmpm.status = 3 AND ovmpm.oust_time IS NOT NULL)
        OR ovmpm.select_status = 6
        ),

        events_with_after AS (
        SELECT
        video_id,
        t,
        delta,
        SUM(delta) OVER (
        PARTITION BY video_id
        ORDER BY t, delta DESC
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        ) AS cnt_after
        FROM events_all
        ),

        events_flagged AS (
        SELECT
        ev.video_id,
        ev.t,
        ev.delta,
        LAG(ev.cnt_after) OVER w AS cnt_before,
        ev.cnt_after,
        CASE
        WHEN ev.delta = -1
        AND NOT EXISTS (
        SELECT 1
        FROM order_video_match_preselect_model x
        JOIN order_video_match xm ON x.match_id = xm.id
        WHERE xm.video_id = ev.video_id
        AND x.add_type = 1
        AND (x.oust_time IS NULL OR x.oust_time > ev.t)
        AND x.add_time = xm.start_time
        )
        THEN 1 ELSE 0
        END AS resume_flag
        FROM events_with_after ev
        WINDOW w AS (
        PARTITION BY ev.video_id
        ORDER BY ev.t, ev.delta DESC
        ROWS BETWEEN UNBOUNDED PRECEDING AND CURRENT ROW
        )
        ),

        first_valid_release AS (
        SELECT
        lm.video_id,
        MIN(ev.t) AS release_time
        FROM events_flagged ev
        JOIN latest_match lm
        ON ev.video_id = lm.video_id
        LEFT JOIN first_ps fp
        ON fp.match_id = lm.match_id
        WHERE
        ev.delta = -1
        AND ev.cnt_after &lt; #{dto.preselectModelNumberOfPits}

        AND (
        (fp.video_id IS NOT NULL AND ev.resume_flag = 1)
        OR fp.video_id IS NULL
        )

        AND NOT (
        fp.add_type  = 1
        AND fp.add_time  = lm.start_time
        AND ev.t          = fp.oust_time
        AND ev.cnt_after >= #{dto.preselectModelNumberOfPits}
        )
        GROUP BY lm.video_id
        ),

        base AS (
        SELECT
        ov.id,
        ov.product_pic,
        ov.video_code,
        ov.product_english,
        ov.platform,
        ov.video_duration,
        ov.video_style,
        ov.pic_count,
        ov.refund_pic_count,
        ov.create_order_biz_user_id,
        COALESCE(ov.rollback_id,0) AS rbid,
        CASE
        WHEN fp.video_id IS NOT NULL
        THEN
        fvr.release_time
        ELSE
        lm.start_time
        END AS release_time,

        CASE
        WHEN fp.video_id IS NOT NULL
        THEN
        fvr.release_time
        ELSE
        lm.start_time
        END AS start_time,
        ov.status_time,
        ov.un_confirm_time,
        lm.shuffled_sort_key
        FROM order_video ov
        JOIN base_ids b        ON b.id = ov.id
        JOIN latest_match lm   ON lm.video_id = ov.id
        LEFT JOIN first_valid_release fvr ON fvr.video_id = ov.id
        LEFT JOIN first_ps fp ON fp.match_id = lm.match_id
        ),

        first_fill AS (
        SELECT
        sub.video_id,
        MIN(sub.t) AS fill_time
        FROM (
        SELECT
        video_id,
        t,
        LAG(cnt_after) OVER (PARTITION BY video_id ORDER BY t, delta DESC) AS cnt_before,
        cnt_after
        FROM events_with_after
        ) sub
        JOIN base b ON b.id = sub.video_id
        WHERE sub.cnt_before &lt; #{dto.preselectModelNumberOfPits}
        AND sub.cnt_after  >= #{dto.preselectModelNumberOfPits}
        AND sub.t >= b.start_time
        GROUP BY sub.video_id
        ),

        initial_used AS (
        SELECT
        b.id AS video_id,
        TIMESTAMPDIFF(SECOND, b.start_time, COALESCE(f.fill_time, NOW())) AS init_sec
        FROM base b
        LEFT JOIN first_fill f ON f.video_id = b.id
        ),

        drop_times AS (
        SELECT
        ev.video_id,
        ev.t AS drop_at
        FROM events_flagged ev
        JOIN first_valid_release fvr ON fvr.video_id = ev.video_id
        JOIN first_fill ff
        ON ff.video_id = ev.video_id
        WHERE ev.delta = -1
        AND ev.cnt_before >= #{dto.preselectModelNumberOfPits}
        AND ev.cnt_after  &lt; #{dto.preselectModelNumberOfPits}
        AND ev.t >= fvr.release_time
        AND ev.t >= ff.fill_time
        ),

        resume_intervals AS (
        SELECT
        d.video_id,
        d.drop_at,
        MIN(f2.t) AS next_fill
        FROM drop_times d
        LEFT JOIN (
        SELECT
        video_id,
        t
        FROM (
        SELECT
        video_id,
        t,
        LAG(cnt_after) OVER (PARTITION BY video_id ORDER BY t, delta DESC) AS cnt_before,
        cnt_after
        FROM events_with_after
        ) z
        WHERE cnt_before &lt; #{dto.preselectModelNumberOfPits} AND cnt_after >= #{dto.preselectModelNumberOfPits}
        ) f2
        ON f2.video_id = d.video_id
        AND f2.t > d.drop_at
        GROUP BY d.video_id, d.drop_at
        ),

        resume_used AS (
        SELECT
        video_id,
        SUM(TIMESTAMPDIFF(SECOND, drop_at, COALESCE(next_fill, NOW()))) AS resume_sec
        FROM resume_intervals
        GROUP BY video_id
        ),

        total_used AS (
        SELECT
        i.video_id,
        i.init_sec + COALESCE(r.resume_sec,0) AS used_sec
        FROM initial_used i
        LEFT JOIN resume_used r ON r.video_id = i.video_id
        )
    </sql>

    <select id="selectOrderOrdinaryModelListByCondition"
            resultType="com.ruoyi.system.api.domain.vo.order.OrderModelListVO">
        <include refid="selectOrderOrdinaryModelListSql"/>
        SELECT
            ov.id,
            ov.product_pic,
            ov.video_code,
            ov.product_english,
            ov.platform,
            ov.video_duration,
            ov.video_style,
            ov.pic_count,
            ov.refund_pic_count,
            ov.create_order_biz_user_id
        FROM base ov
                 LEFT JOIN total_used t ON t.video_id=ov.id
        WHERE
            (
                ov.un_confirm_time &gt;= #{dto.modelTerminalAllListOldDataEndTime}
                    AND t.used_sec >= #{dto.releaseOverTime} * 3600
                )
           OR (
            ov.un_confirm_time &lt; #{dto.modelTerminalAllListOldDataEndTime}
                AND (
                ov.release_time &lt;= NOW() - INTERVAL #{dto.releaseOverTime} HOUR
                )
            )
        order by ov.shuffled_sort_key
    </select>
    <select id="getOrderOrdinaryModelCountByCondition" resultType="java.lang.Long">
        <include refid="selectOrderOrdinaryModelListSql"/>
        -- 最终输出，只对 un_confirm_time >= 2025-06-20 应用 48h 规则
        SELECT
            COUNT(*)
        FROM base b
            LEFT JOIN total_used t ON t.video_id=b.id
        WHERE
        (
            b.un_confirm_time &gt;= #{dto.modelTerminalAllListOldDataEndTime}
            AND t.used_sec >= #{dto.releaseOverTime} * 3600
        )
        OR (
            b.un_confirm_time &lt; #{dto.modelTerminalAllListOldDataEndTime}
            AND (
                b.release_time &lt;= NOW() - INTERVAL #{dto.releaseOverTime} HOUR
            )
        )
        OR b.rbid != 0
    </select>
</mapper>

