package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.ErrorConstants;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.PreAuthorizeException;
import com.ruoyi.common.core.utils.PageUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.order.OrderResource;
import com.ruoyi.system.api.domain.entity.order.OrderVideo;
import com.ruoyi.system.api.domain.entity.order.OrderVideoUploadLink;
import com.ruoyi.system.api.domain.entity.order.OrderVideoUploadLinkRecord;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountDetailVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.wnkx.order.config.OrderEditProperties;
import com.wnkx.order.mapper.OrderVideoUploadLinkMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/26 17:57
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class OrderVideoUploadLinkServiceImpl extends ServiceImpl<OrderVideoUploadLinkMapper, OrderVideoUploadLink> implements OrderVideoUploadLinkService {

    private final RemoteService remoteService;
    private final OrderVideoRoastService orderVideoRoastService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderVideoUploadLinkRecordService orderVideoUploadLinkRecordService;
    private final OrderEditProperties orderEditProperties;
    private final IOrderVideoRefundService orderVideoRefundService;
    private final RedisService redisService;
    private final OrderVideoFeedBackMaterialInfoTaskDetailService orderVideoFeedBackMaterialInfoTaskDetailService;
    private final IOrderVideoService orderVideoService;
    private final OrderResourceService orderResourceService;


    /**
     * 剪辑管理-上传账号-下拉框
     */
    @Override
    public Set<String> uploadAccountSelect() {
        return baseMapper.uploadAccountSelect();
    }

    /**
     * 编辑素材
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editUploadMaterial(OrderVideoUploadLinkDTO dto) {
        OrderVideoUploadLink orderVideoUploadLink = baseMapper.selectById(dto.getId());
        Assert.notNull(orderVideoUploadLink, "数据不存在");
        Assert.isTrue(UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode().equals(orderVideoUploadLink.getStatus()), "状态非待上传，不支持修改~");

        orderVideoUploadLink.setNeedUploadLink(dto.getNeedUploadLink());
        orderVideoUploadLink.setVideoTitle(dto.getVideoTitle());
        orderVideoUploadLink.setVideoCover(dto.getVideoCover());
        orderVideoUploadLink.setRemark(dto.getRemark());
        orderVideoUploadLink.setAsin(StringUtils.extractAmazonGoodsId(dto.getNeedUploadLink()));
        baseMapper.updateById(orderVideoUploadLink);

        OrderVideoUploadLinkRecord havenTUploadedByUploadLinkId = orderVideoUploadLinkRecordService.getHavenTUploadedByUploadLinkId(dto.getId());
        havenTUploadedByUploadLinkId.setNeedUploadLink(dto.getNeedUploadLink());
        havenTUploadedByUploadLinkId.setVideoTitle(dto.getVideoTitle());
        havenTUploadedByUploadLinkId.setVideoCover(dto.getVideoCover());
        havenTUploadedByUploadLinkId.setRemark(dto.getRemark());
        orderVideoUploadLinkRecordService.updateById(havenTUploadedByUploadLinkId);
    }

    /**
     * 插入一条已完成的无需上传的记录
     */
    @Override
    public void saveFinisheds(List<Long> videoIds) {
        if (CollUtil.isEmpty(videoIds)) {
            return;
        }

        DateTime date = DateUtil.date();

        List<OrderVideoUploadLink> orderVideoUploadLinks = new ArrayList<>();
        for (Long videoId : videoIds) {
            OrderVideoUploadLink orderVideoUploadLink = new OrderVideoUploadLink();
            orderVideoUploadLink.setVideoId(videoId);
            orderVideoUploadLink.setStatus(UploadLinkStatusEnum.NO_UPLOAD_REQUIRED.getCode());
            orderVideoUploadLink.setStatusTime(date);
            orderVideoUploadLinks.add(orderVideoUploadLink);
        }
        baseMapper.saveBatch(orderVideoUploadLinks);
    }

    /**
     * 插入一条已完成的无需上传的记录
     */
    @Override
    public void saveFinished(Long videoId) {
        OrderVideoUploadLink orderVideoUploadLink = new OrderVideoUploadLink();
        orderVideoUploadLink.setVideoId(videoId);
        orderVideoUploadLink.setStatus(UploadLinkStatusEnum.NO_UPLOAD_REQUIRED.getCode());
        orderVideoUploadLink.setStatusTime(DateUtil.date());
        baseMapper.insert(orderVideoUploadLink);
    }

    /**
     * 剪辑管理-取消上传
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelUploadLink(OrderUploadLinkDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY + dto.getId(), CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");
        try {
            OrderVideoUploadLink orderVideoUploadLink = baseMapper.selectById(dto.getId());
            Assert.notNull(orderVideoUploadLink, "数据不存在");
            Assert.isTrue(UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode().equals(orderVideoUploadLink.getStatus()) || UploadLinkStatusEnum.UPLOAD_TO_BE_CONFIRMED.getCode().equals(orderVideoUploadLink.getStatus()), "请刷新页面后重试~");

            orderVideoUploadLink.setStatus(UploadLinkStatusEnum.CANCEL_UPLOAD.getCode());
            orderVideoUploadLink.setStatusTime(DateUtil.date());
            orderVideoUploadLink.setOperateRemark(dto.getOperateRemark());
            orderVideoUploadLink.setCloseBy(SecurityUtils.getUsername());
            orderVideoUploadLink.setCloseById(SecurityUtils.getUserId());
            orderVideoUploadLink.setCloseReason(EditCloseReasonEnum.CANCEL_UPLOAD.getCode());
            baseMapper.updateById(orderVideoUploadLink);

            OrderVideoUploadLinkRecord updateRecord = new OrderVideoUploadLinkRecord();
            updateRecord.setUploadLinkId(orderVideoUploadLink.getId());
            updateRecord.setStatus(orderVideoUploadLink.getStatus());
            updateRecord.setOperateRemark(orderVideoUploadLink.getOperateRemark());
            orderVideoUploadLinkRecordService.updateLatestUploadLinkRecord(updateRecord);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY + dto.getId());
        }
    }

    /**
     * 剪辑管理-上传失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadLinkFail(OrderUploadLinkDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY + dto.getId(), CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");
        try {
            OrderVideoUploadLink orderVideoUploadLink = baseMapper.selectById(dto.getId());
            Assert.notNull(orderVideoUploadLink, "数据不存在");
            Assert.isTrue(UploadLinkStatusEnum.UPLOAD_TO_BE_CONFIRMED.getCode().equals(orderVideoUploadLink.getStatus()), "请刷新页面后重试~");

            orderVideoUploadLink.setStatus(UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode());
            orderVideoUploadLink.setStatusTime(DateUtil.date());
            orderVideoUploadLink.setOperateRemark(dto.getOperateRemark());
            baseMapper.uploadLinkFail(orderVideoUploadLink);

            OrderVideoUploadLinkRecord updateRecord = new OrderVideoUploadLinkRecord();
            updateRecord.setUploadLinkId(orderVideoUploadLink.getId());
            updateRecord.setStatus(UploadLinkStatusEnum.FAIL_TO_UPLOAD.getCode());
            updateRecord.setOperateRemark(orderVideoUploadLink.getOperateRemark());
            orderVideoUploadLinkRecordService.updateLatestUploadLinkRecord(updateRecord);

            OrderVideoUploadLinkRecord saveRecord = new OrderVideoUploadLinkRecord();
            saveRecord.setUploadLinkId(orderVideoUploadLink.getId());
            saveRecord.setNeedUploadLink(orderVideoUploadLink.getNeedUploadLink());
            saveRecord.setVideoTitle(orderVideoUploadLink.getVideoTitle());
            saveRecord.setUploadAccount(orderVideoUploadLink.getUploadAccount());
            saveRecord.setVideoCover(orderVideoUploadLink.getVideoCover());
            saveRecord.setRemark(orderVideoUploadLink.getRemark());
            saveRecord.setStatus(UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode());
            orderVideoUploadLinkRecordService.insertUploadLinkRecord(saveRecord);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY + dto.getId());
        }
    }

    /**
     * 剪辑管理-标记为待确认上传
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markUploadConfirm(Long uploadLinkId) {
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY + uploadLinkId, CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");
        try {
            OrderVideoUploadLink orderVideoUploadLink = baseMapper.selectById(uploadLinkId);
            Assert.notNull(orderVideoUploadLink, "数据不存在");
            Assert.isTrue(CharSequenceUtil.isNotBlank(orderVideoUploadLink.getUploadAccount()), "请先关联上传账号");
            Assert.isTrue(UploadLinkStatusEnum.HAVEN_T_UPLOADED.getCode().equals(orderVideoUploadLink.getStatus()), "请刷新页面后重试~");

            orderVideoUploadLink.setUploadTime(DateUtil.date());
            orderVideoUploadLink.setUploadUserId(SecurityUtils.getUserId());
            orderVideoUploadLink.setUploadUserName(SecurityUtils.getUsername());
            orderVideoUploadLink.setStatus(UploadLinkStatusEnum.UPLOAD_TO_BE_CONFIRMED.getCode());
            orderVideoUploadLink.setStatusTime(DateUtil.date());

            baseMapper.updateById(orderVideoUploadLink);

            OrderVideoUploadLinkRecord orderVideoUploadLinkRecord = new OrderVideoUploadLinkRecord();
            orderVideoUploadLinkRecord.setUploadLinkId(orderVideoUploadLink.getId());
            orderVideoUploadLinkRecord.setUploadAccount(orderVideoUploadLink.getUploadAccount());
            orderVideoUploadLinkRecord.setStatus(orderVideoUploadLink.getStatus());
            orderVideoUploadLinkRecord.setUploadTime(orderVideoUploadLink.getUploadTime());
            orderVideoUploadLinkRecord.setUploadUserId(orderVideoUploadLink.getUploadUserId());
            orderVideoUploadLinkRecord.setUploadUserName(orderVideoUploadLink.getUploadUserName());
            orderVideoUploadLinkRecordService.updateLatestUploadLinkRecord(orderVideoUploadLinkRecord);
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY + uploadLinkId);
        }
    }

    /**
     * 剪辑管理-标记上传账号-下拉框
     */
    @Override
    public List<MarkUploadAccountSelectVO> getMarkUploadAccountSelect(Long uploadLinkId) {
        List<String> uploadAccounts;
        if (ObjectUtil.isNotNull(uploadLinkId)) {
            OrderVideoUploadLink orderVideoUploadLink = baseMapper.selectById(uploadLinkId);
            Assert.notNull(orderVideoUploadLink, "数据不存在");
            uploadAccounts = baseMapper.getUploadAccountByASIN(orderVideoUploadLink.getAsin(), orderVideoUploadLink.getId());
        } else {
            uploadAccounts = new ArrayList<>();
        }

        return orderEditProperties.getUploadAccount().stream().map(item -> {
            MarkUploadAccountSelectVO markUploadAccountSelectVO = new MarkUploadAccountSelectVO();
            markUploadAccountSelectVO.setUploadAccount(item);
            markUploadAccountSelectVO.setIsSelect(uploadAccounts.contains(item));
            return markUploadAccountSelectVO;
        }).collect(Collectors.toList());
    }

    /**
     * 剪辑管理-标记上传账号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markUploadAccount(MarkUploadAccountDTO dto) {
        OrderVideoUploadLink orderVideoUploadLink = baseMapper.selectById(dto.getUploadLinkId());
        Assert.notNull(orderVideoUploadLink, "数据不存在");

        orderVideoUploadLink.setUploadAccount(dto.getUploadAccount());
        List<String> uploadAccounts = baseMapper.getUploadAccountByASIN(orderVideoUploadLink.getAsin(), orderVideoUploadLink.getId());
        Assert.isFalse(uploadAccounts.contains(dto.getUploadAccount()), "该账号同个ASIN已上传");
        baseMapper.updateById(orderVideoUploadLink);

        OrderVideoUploadLinkRecord havenTUploadedByUploadLinkId = orderVideoUploadLinkRecordService.getHavenTUploadedByUploadLinkId(dto.getUploadLinkId());
        havenTUploadedByUploadLinkId.setUploadAccount(dto.getUploadAccount());
        orderVideoUploadLinkRecordService.updateById(havenTUploadedByUploadLinkId);
    }

    /**
     * 剪辑管理-历史上传记录
     */
    @Override
    public List<HistoryUploadRecordVO> getHistoryUploadRecord(Long uploadLinkId) {
        return orderVideoUploadLinkRecordService.getHistoryUploadRecord(uploadLinkId);
    }

    /**
     * 剪辑管理-待上传、已完成 拍摄模特下拉框
     */
    @Override
    public List<ModelSelectVO> uploadLinkListSelectShootModel(List<Integer> status) {
        List<Long> shootModelIds = baseMapper.getShootModelId(status);
        if (CollUtil.isEmpty(shootModelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(shootModelIds);
        return remoteService.modelSelect(modelListDTO);
    }

    /**
     * 剪辑管理-待上传、已完成 列表
     */
    @Override
    public List<UploadLinkListVO> selectUploadLinkListByCondition(UploadLinkListDTO dto) {
        if (CharSequenceUtil.isNotBlank(dto.getKeyword())) {
            List<SysUser> userList = remoteService.getUserList(SysUserListDTO.builder().userName(dto.getKeyword()).build());
            dto.setBackUserIds(userList.stream().map(SysUser::getUserId).collect(Collectors.toSet()));
        }

        OrderByDto orderByDto = new OrderByDto();
        orderByDto.setField("ovul.create_time", OrderByDto.DIRECTION.ASC);
        PageUtils.startPage(orderByDto);
        List<UploadLinkListVO> list = baseMapper.selectUploadLinkListByCondition(dto);
        if (CollUtil.isEmpty(list)) {
            return list;
        }

        List<String> referencePicIds = list.stream().map(UploadLinkListVO::getReferencePicId).filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        List<Long> resourceIds = new ArrayList<>(StringUtils.splitToLong(referencePicIds, StrUtil.COMMA));
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

        Set<Long> shootModelIds = list.stream().map(UploadLinkListVO::getShootModelId).collect(Collectors.toSet());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(shootModelIds);

        Set<Long> contactIds = list.stream().map(UploadLinkListVO::getContactId).collect(Collectors.toSet());
        Set<Long> issueIds = list.stream().map(UploadLinkListVO::getIssueId).collect(Collectors.toSet());
        Set<Long> uploadUserIds = list.stream().map(UploadLinkListVO::getUploadUserId).collect(Collectors.toSet());
        contactIds.addAll(issueIds);
        contactIds.addAll(uploadUserIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(contactIds).build());

        List<Long> videoIds = list.stream().map(UploadLinkListVO::getVideoId).collect(Collectors.toList());
        Map<Long, List<VideoTaskOrderVO>> videoTaskOrderVOMap = SpringUtils.getBean(IOrderService.class).getVideoTaskOrderVOMap(videoIds);

        OrderVideoRefundSimpleListVO videoRefundMap = SpringUtils.getBean(IOrderService.class).getVideoRefundMap(videoIds);

        for (UploadLinkListVO uploadLinkListVO : list) {
            uploadLinkListVO.setShootModel(modelSimpleMap.get(uploadLinkListVO.getShootModelId()));
            uploadLinkListVO.setCoverAndTitleChange(ObjectUtil.notEqual(uploadLinkListVO.getVideoTitleFirst(), uploadLinkListVO.getVideoTitle()) || ObjectUtil.notEqual(uploadLinkListVO.getVideoCoverFirst(), uploadLinkListVO.getVideoCover()));
            //  参考图片
            List<Long> referencePicIdList = StringUtils.splitToLong(uploadLinkListVO.getReferencePicId(), StrUtil.COMMA);
            for (Long referencePicId : referencePicIdList) {
                OrderResource resource = resourceMap.get(referencePicId);
                if (resource != null) {
                    uploadLinkListVO.getReferencePic().add(resource.getObjectKey());
                }
            }

            //  订单退款信息
            uploadLinkListVO.setOrderVideoRefund(videoRefundMap.getMapVo().get(uploadLinkListVO.getVideoId()));
            uploadLinkListVO.setOrderVideoRefundList(videoRefundMap.getListVo().get(uploadLinkListVO.getVideoId()));

            uploadLinkListVO.setContact(userMap.get(uploadLinkListVO.getContactId()));
            uploadLinkListVO.setIssue(userMap.get(uploadLinkListVO.getIssueId()));
            uploadLinkListVO.setUploadUserName(userMap.getOrDefault(uploadLinkListVO.getUploadUserId(), new UserVO()).getName());

            List<VideoTaskOrderVO> videoTaskOrderVOS = videoTaskOrderVOMap.get(uploadLinkListVO.getVideoId());
            if (CollUtil.isNotEmpty(videoTaskOrderVOS)) {
                List<VideoTaskOrderVO> afterSaleTaskVOS = videoTaskOrderVOS.stream().filter(item -> OrderTaskTypeEnum.AFTER_SALE.getCode().equals(item.getTaskType())).collect(Collectors.toList());
                List<VideoTaskOrderVO> workOrderVOS = videoTaskOrderVOS.stream().filter(item -> OrderTaskTypeEnum.WORK_ORDER.getCode().equals(item.getTaskType())).collect(Collectors.toList());
                //  添加售后单状态
                if (CollUtil.isNotEmpty(afterSaleTaskVOS)) {
                    if (afterSaleTaskVOS.stream().anyMatch(item -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(item.getStatus()) || OrderTaskStatusEnum.HANDLE_ING.getCode().equals(item.getStatus()))) {
                        uploadLinkListVO.setAfterSaleTaskStatus(OrderTaskStatusEnum.UN_HANDLE.getCode());
                    } else if (afterSaleTaskVOS.stream().anyMatch(item -> OrderTaskStatusEnum.HANDLE.getCode().equals(item.getStatus()))) {
                        uploadLinkListVO.setAfterSaleTaskStatus(OrderTaskStatusEnum.HANDLE.getCode());
                    }
                }

                //  添加工单状态
                if (CollUtil.isNotEmpty(workOrderVOS)) {
                    if (workOrderVOS.stream().anyMatch(item -> OrderTaskStatusEnum.UN_HANDLE.getCode().equals(item.getStatus()))) {
                        uploadLinkListVO.setWorkOrderTaskStatus(OrderTaskStatusEnum.UN_HANDLE.getCode());
                    } else if (workOrderVOS.stream().anyMatch(item -> OrderTaskStatusEnum.HANDLE.getCode().equals(item.getStatus()))) {
                        uploadLinkListVO.setWorkOrderTaskStatus(OrderTaskStatusEnum.HANDLE.getCode());
                    }
                }
            }
        }

        return list;
    }

    /**
     * 通过视频订单id查询上传素材列表
     */
    @Override
    public List<OrderVideoUploadLinkVO> selectListByVideoIds(List<Long> videoIds) {
        List<OrderVideoUploadLink> list = baseMapper.selectListByVideoIds(videoIds);
        List<OrderVideoUploadLinkVO> orderVideoUploadLinkVOS = BeanUtil.copyToList(list, OrderVideoUploadLinkVO.class);
        assembleListData(orderVideoUploadLinkVOS);
        return orderVideoUploadLinkVOS;
    }

    /**
     * 剪辑管理-上传成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadLink(OrderUploadLinkDTO dto) {
        Assert.isTrue(redisService.getLock(CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY + dto.getId(), CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY_SECOND), "数据处理中，请稍后重试~");

        try {
            OrderVideoUploadLink orderVideoUploadLink = baseMapper.selectById(dto.getId());
            Assert.notNull(orderVideoUploadLink, "该视频订单无需上传素材至关联区");
            Assert.isTrue(UploadLinkStatusEnum.UPLOAD_TO_BE_CONFIRMED.getCode().equals(orderVideoUploadLink.getStatus()), "请刷新页面后重试~");

            OrderVideo orderVideo = orderVideoService.getById(orderVideoUploadLink.getVideoId());
            Assert.notNull(orderVideo, "视频订单不存在");
            orderVideoService.checkVideoStatus(orderVideo, OrderStatusEnum.FINISHED);

            List<Long> refundVideoIds = orderVideoRefundService.selectVideoIdByRefund(List.of(orderVideoUploadLink.getVideoId()));
            Assert.isTrue(CollUtil.isEmpty(refundVideoIds), () -> new PreAuthorizeException(ErrorConstants.REFUND_ERROR_TIPS));

            DateTime date = DateUtil.date();

            orderVideoUploadLink.setStatus(UploadLinkStatusEnum.HAVE_ALREADY_UPLOADED.getCode());
            orderVideoUploadLink.setStatusTime(date);
            orderVideoUploadLink.setUploadLink(dto.getUploadLink());
            baseMapper.updateById(orderVideoUploadLink);

            OrderVideoUploadLinkRecord orderVideoUploadLinkRecord = new OrderVideoUploadLinkRecord();
            orderVideoUploadLinkRecord.setUploadLinkId(orderVideoUploadLink.getId());
            orderVideoUploadLinkRecord.setUploadLink(orderVideoUploadLink.getUploadLink());
            orderVideoUploadLinkRecord.setStatus(orderVideoUploadLink.getStatus());
            orderVideoUploadLinkRecordService.updateLatestUploadLinkRecord(orderVideoUploadLinkRecord);

            orderVideoFlowNodeDiagramService.setNodeCompleteTime(dto.getId(), OrderVideoFlowNodeEnum.ORDER_COMPLETION);

            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.UPLOAD_LINK.getEventName(), null, OrderVideoOperateTypeEnum.UPLOAD_LINK.getIsPublic(), null, OrderVideoOperateDTO.builder().videoId(orderVideoUploadLink.getVideoId()).eventContent(OrderVideoOperateTypeEnum.UPLOAD_LINK.getEventContent()).build());
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ORDER_COMPLETION.getEventName(), null, OrderVideoOperateTypeEnum.ORDER_COMPLETION.getIsPublic(), null, OrderVideoOperateDTO.builder().videoId(dto.getId()).eventContent(OrderVideoOperateTypeEnum.ORDER_COMPLETION.getEventContent()).eventExecuteTime(DateUtil.offsetSecond(date, 3)).build());

            orderVideoFeedBackMaterialInfoTaskDetailService.finishTaskByVideoIdAndRollbackId(orderVideo.getId(), orderVideo.getRollbackId());
        } finally {
            redisService.releaseLock(CacheConstants.ORDER_UPLOAD_LINK_LOCK_KEY + dto.getId());
        }
    }

    /**
     * 上传素材
     */
    @Override
    public OrderVideoUploadLinkVO getUploadMaterial(Long videoId) {
        OrderVideoUploadLink orderVideoUploadLink = baseMapper.getOneByVideoId(videoId);
        if (ObjectUtil.isNull(orderVideoUploadLink)) {
            return null;
        }
        OrderVideoUploadLinkVO orderVideoUploadLinkVO = BeanUtil.copyProperties(orderVideoUploadLink, OrderVideoUploadLinkVO.class);

        assembleListData(List.of(orderVideoUploadLinkVO));

        if (UserTypeConstants.USER_TYPE == SecurityUtils.getLoginUserType()) {
            orderVideoUploadLinkVO.setRemark(null);
            orderVideoUploadLinkVO.setUploadUser(null);
            orderVideoUploadLinkVO.setUploadTime(null);
        }

        orderVideoUploadLinkVO.setIsRoast(orderVideoRoastService.isRoast(videoId));
        return orderVideoUploadLinkVO;
    }

    private void assembleListData(List<OrderVideoUploadLinkVO> orderVideoUploadLinkVOS) {
        Set<Long> companyIds = orderVideoUploadLinkVOS.stream().filter(item -> UploadObjectEnum.COMPANY.getCode().equals(item.getObject())).map(OrderVideoUploadLinkVO::getUserId).collect(Collectors.toSet());
        Set<Long> backIds = orderVideoUploadLinkVOS.stream().filter(item -> UploadObjectEnum.BACK.getCode().equals(item.getObject())).map(OrderVideoUploadLinkVO::getUserId).collect(Collectors.toSet());
        Set<Long> uploadUserIds = orderVideoUploadLinkVOS.stream().map(OrderVideoUploadLinkVO::getUploadUserId).collect(Collectors.toSet());
        backIds.addAll(uploadUserIds);

        List<BusinessAccountDetailVO> businessAccountDetailVOS = remoteService.queryMerchantByBusinessIdsAndAccountIds(null, companyIds);
        Map<Long, BusinessAccountDetailVO> accountMap = RemoteService.RemoteUtil.getAccountMap(businessAccountDetailVOS);
        Map<Long, UserVO> userMap = remoteService.getUserMap(SysUserListDTO.builder().userId(backIds).build());

        for (OrderVideoUploadLinkVO orderVideoUploadLinkVO : orderVideoUploadLinkVOS) {
            if (UploadObjectEnum.COMPANY.getCode().equals(orderVideoUploadLinkVO.getObject())) {
                orderVideoUploadLinkVO.setCompany(accountMap.get(orderVideoUploadLinkVO.getUserId()));
            }
            orderVideoUploadLinkVO.setUploadUser(userMap.get(orderVideoUploadLinkVO.getUploadUserId()));
            orderVideoUploadLinkVO.setBack(userMap.get(orderVideoUploadLinkVO.getUserId()));
        }
    }

    @Override
    public OrderVideoUploadLinkSimpleVO getOrderVideoUploadLinkSimpleVO(Long videoId) {
        return BeanUtil.copyProperties(getUploadMaterial(videoId), OrderVideoUploadLinkSimpleVO.class);
    }

    /**
     * 新增上传素材
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveOrderVideoUploadLink(OrderVideoUploadLinkDTO dto) {
        OrderVideoUploadLink orderVideoUploadLink = BeanUtil.copyProperties(dto, OrderVideoUploadLink.class);
        DateTime date = DateUtil.date();
        orderVideoUploadLink.setTime(date);
        orderVideoUploadLink.setStatusTime(date);
        orderVideoUploadLink.setAsin(StringUtils.extractAmazonGoodsId(dto.getNeedUploadLink()));
        orderVideoUploadLink.setVideoTitleFirst(dto.getVideoTitle());
        orderVideoUploadLink.setVideoCoverFirst(dto.getVideoCover());
        baseMapper.insert(orderVideoUploadLink);

        OrderVideoUploadLinkRecord orderVideoUploadLinkRecord = new OrderVideoUploadLinkRecord();
        orderVideoUploadLinkRecord.setUploadLinkId(orderVideoUploadLink.getId());
        orderVideoUploadLinkRecord.setNeedUploadLink(orderVideoUploadLink.getNeedUploadLink());
        orderVideoUploadLinkRecord.setVideoTitle(orderVideoUploadLink.getVideoTitle());
        orderVideoUploadLinkRecord.setVideoCover(orderVideoUploadLink.getVideoCover());
        orderVideoUploadLinkRecord.setRemark(orderVideoUploadLink.getRemark());
        orderVideoUploadLinkRecordService.insertUploadLinkRecord(orderVideoUploadLinkRecord);
    }

    @Override
    public List<OrderVideoUploadLink> selectListByDto(UploadLinkListDTO listDTO) {
        return baseMapper.selectListByDto(listDTO);
    }
}
