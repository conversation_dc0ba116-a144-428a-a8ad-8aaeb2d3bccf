package com.wnkx.order.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.dto.biz.datastatistics.ModelOrderCommissionAnalysisDTO;
import com.ruoyi.system.api.domain.dto.biz.model.ModelDataTableListDTO;
import com.ruoyi.system.api.domain.dto.biz.translate.TranslateBatchDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatch;
import com.ruoyi.system.api.domain.entity.order.OrderVideoMatchPreselectModel;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.ModelBasicDataVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelDataTableListVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelOrderCommissionAnalysisVO;
import com.ruoyi.system.api.domain.vo.order.datastatistics.ModelSuccessMatchCountVO;
import org.springframework.validation.annotation.Validated;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * <AUTHOR>
 * @date 2024/11/15 15:56
 */
public interface OrderVideoMatchService extends IService<OrderVideoMatch> {

    /**
     * 批量新增匹配单
     */
    void saveBatchOrderVideoMatch(List<SaveBatchOrderVideoMatchDTO> dtoList);

    /**
     * 商家更换模特 对旧的选定的模特进行淘汰
     * @param videoId   视频订单id
     */
    void changeModel(Long videoId, String reason);

    /**
     * 确认提交预选模特
     */
    void submitPreselectModel(SubmitPreselectModelDTO dto);

    /**
     * 查询视频订单已淘汰模特数量
     */
    List<CountVideoOutModelVO> countVideoOutModel(Collection<Long> videoIds);

    /**
     * 查询视频订单已淘汰模特数量
     */
    Long countVideoOutModel(Long videoId);

    /**
     * 通过视频订单id查询历史关联的预选模特
     */
    List<OrderVideoMatchVO> selectHistoryMatchListByVideoId(Long videoId, boolean isHistory);

    /**
     * 通过视频订单id查询活跃的关联的预选模特（运营端订单列表）
     */
    List<VideoMatchOrderVO> selectActiveListByVideoIds(List<Long> videoIds);

    /**
     * 添加预选模特
     */
    AddDistributionErrorVO addPreselectModel(AddPreselectModelDTO dto);

    /**
     * 更改预选模特状态
     */
    void editPreselectModel(EditPreselectModelDTO editPreselectModelDTO);

    /**
     * 更新预选模特列表为已淘汰
     */
    void outPreselectModel(List<OutPreselectModelDTO> dtoList);

    /**
     * 通过视频订单id和模特id查询未对接或已对接的模特
     *
     * @param videoId    订单id
     * @param modelId 模特id
     * @return 未对接的模特
     */
    OrderVideoMatchPreselectModel getUnJointedOrJointedByVideoIdAndModelId(Long videoId, Long modelId);

    /**
     * 通过视频订单id 更改模特记录状态 为 已确认拍摄
     */
    void updateModelSelectStatusToConfirmByVideoId(List<Long> videoIds);

    /**
     * 预选管理-模特匹配-订单池
     */
    List<OrderPoolListVO> selectOrderPoolListByCondition(@Validated OrderPoolListDTO orderPoolListDTO);

    /**
     * 查询当前匹配单活跃的预选模特
     */
    List<OrderVideoMatchPreselectModelVO> selectActivePreselectModelListByMatchId(Long matchId);

    /**
     * 预选管理-模特匹配-我的预选-沟通中
     */
    List<MyPreselectDockingListVO> selectMyPreselectDockingList(MyPreselectDockingListDTO dto);

    /**
     * 预选管理-模特匹配-我的预选-结束预选
     */
    List<MyPreselectEndMatchListVO> selectMyPreselectEndMatchList(MyPreselectEndMatchListDTO dto);

    /**
     * 清空匹配单的标记
     */
    void clearFlag(Long matchId, Integer checkSelected);

    /**
     * 清空匹配单的标记
     */
    void clearFlag(List<Long> matchIds, Integer checkSelected);

    /**
     * 选定模特信息
     */
    MarkOrderVO selectedModelInfo(Long matchId, Long modelId);

    /**
     * 选定模特信息-主携带订单下拉框
     */
    List<ModelCarryVO> selectedModelMainCarryListByMatchId(Long matchId, Long modelId);

    /**
     * 选定模特
     */
    void selectedModel(MarkOrderDTO markOrderDTO);

    /**
     * 修改选定模特
     */
    void editSelectedModel(MarkOrderDTO markOrderDTO);

    /**
     * 结束预选-预选记录
     */
    HistoryPreselectModelVO preselectionRecord(Long matchId);

    /**
     * 通过添加预选时间筛选符合条件的匹配单的videoId（给订单列表筛选用）
     */
    Set<Long> selectVideoIdByAddPreselectTimeOfOrderVideo(String addPreselectTimeBegin, String addPreselectTimeEnd);

    /**
     * 暂停匹配单
     */
    void pauseMatch(VideoPauseMatchDTO videoPauseMatchDTO);

    /**
     * 继续模特匹配
     */
    void continueMatch(Long videoId);

    /**
     * 修改订单信息
     */
    void editOrderVideo(OrderOperationVideoDTO orderOperationVideoDTO);

    /**
     * 设置模特选择记录的状态为卖方取消
     */
    void updateModelSelectStatusToCancelByVideoId(List<Long> videoIds);

    /**
     * 通过视频订单ID获取匹配单
     */
    OrderVideoMatch getActiveByVideoId(Long videoId);

    /**
     * 翻译匹配单拍摄要求并存储翻译结果
     */
    List<String> translateShootRequired(Long matchId, TranslateBatchDTO translateBatchDTO);

    /**
     * 视频订单回退淘汰预选模特
     */
    void rollbackOrderOustPreselectModel(Long videoId,String cause);

    /**
     * 主携带订单 若底下有携带的订单 这些携带的订单调整为排单类型
     */
    void rollbackOrderUpdateCarryOrder(Long videoId);

    /**
     * 获取模特剩余需携带订单数
     */
    List<ModelCarryVO> getModelLeftCarryCountByModelIds(Collection<Long> modelIds);

    /**
     * 获取不同于视频订单carry_ignore的匹配单
     */
    List<OrderVideoMatch> getNotCarryIgnoreMatch();

    /**
     * 通过视频订单ID获取被商家驳回的模特ID
     */
    AddPreselectRemoteVO selectRejectModelIdByVideoId(Long videoId);

    /**
     * 校验当前模特是否有其他的相同产品链接的订单
     */
    boolean checkModelAnOrderWithTheSameProductLinkExists(Long modelId, String productLink);

    /**
     * 模特数据统计-获取模特基础数据
     */
    ModelBasicDataVO getModelBasicData();

    /**
     * 模特数据-订单佣金分析 OR 合作深度佣金分析
     */
    ModelOrderCommissionAnalysisVO getModelOrderCommissionAnalysis(ModelOrderCommissionAnalysisDTO dto);

    /**
     * 模特数据-成功匹配次数
     */
    ModelSuccessMatchCountVO getModelSuccessMatchCount();

    /**
     * 通过提交日期查询最新的匹配单
     */
    List<OrderVideoMatch> selectLastListBySubmitTime(String beginDate, String endDate);

    /**
     * 通过提交日期查询提交过的匹配单
     */
    List<OrderVideoMatch> selectListBySubmitTime(String beginDate, String endDate);

    /**
     * 我的预选-完成匹配-拍摄模特下拉框
     */
    List<ModelSelectVO> myPreselectShootModelSelect(String keyword);

    /**
     * 获取平均匹配时长（天）
     *
     * @return 平均匹配时长（天）
     */
    BigDecimal getAverageMatchingDuration(String date);

    /**
     * 通过视频订单ID获取预选模特拍摄注意事项
     */
    String getPreselectModelShootAttentionByVideoId(Long videoId);

    /**
     * 预选管理-添加分发
     */
    AddDistributionErrorVO addDistribution(AddDistributionDTO dto);

    /**
     * 查询视频订单历史选择数据
     */
    List<HistoryPreselectModelCountVO> getHistoryPreselectModelCountList(List<Long> videoIds);

    void refreshModelAllSort();

    /**
     * 查询模特数据表列表
     */
    List<ModelDataTableListVO> selectModelDataTableListByCondition(ModelDataTableListDTO modelDataTableListDTO) throws ExecutionException, InterruptedException;

    boolean hasSelectionManagementPermission();

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表
     */
    List<MyPreselectDockingListVO> selectMyPreselectFailListByCondition(MyPreselectFailListDTO dto);

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-排单推荐列表
     */
    List<MyPreselectFailListRecommendListVO> selectMyPreselectFailListRecommendList(Long videoId, Long modelId, Boolean isOrderPlacementRecommendation);

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-添加排单推荐
     */
    void addRecommend(AddRecommendDTO dto);

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-同链接订单
     */
    List<OrderTheSameProductListVO> selectOrderTheSameProductList(String productLink);

    /**
     * 获取可凑单的模特
     */
    List<ModelInfoVO> getCanMatchModel(List<Long> modelIds);

    /**
     * 预选管理-模特匹配-我的预选-失败待确认列表-添加凑单推荐
     */
    void addMatchRecommend(List<AddMatchRecommendDTO> dtoList);


    List<MatchBizUserMappingVO> selectBizUserIdsByMatchIds(Set<Long> matchIds);
}
