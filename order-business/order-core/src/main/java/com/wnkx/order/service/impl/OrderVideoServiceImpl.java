package com.wnkx.order.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.constant.CacheConstants;
import com.ruoyi.common.core.constant.DelayQueueConstant;
import com.ruoyi.common.core.constant.UserTypeConstants;
import com.ruoyi.common.core.domain.dto.OrderByDto;
import com.ruoyi.common.core.domain.vo.TagVO;
import com.ruoyi.common.core.domain.vo.UserVO;
import com.ruoyi.common.core.enums.*;
import com.ruoyi.common.core.exception.Biz200Exception;
import com.ruoyi.common.core.exception.ServiceException;
import com.ruoyi.common.core.utils.DateUtils;
import com.ruoyi.common.core.utils.ObjectUtils;
import com.ruoyi.common.core.utils.SpringUtils;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.redis.service.DelayQueueService;
import com.ruoyi.common.redis.service.RedisService;
import com.ruoyi.common.security.utils.SecurityUtils;
import com.ruoyi.system.api.domain.bo.biz.datastatistics.ModelOrderScheduledDataBO;
import com.ruoyi.system.api.domain.dto.BusinessAccountDetailDTO;
import com.ruoyi.system.api.domain.dto.ModelListDTO;
import com.ruoyi.system.api.domain.dto.OrderVideoStatisticsDTO;
import com.ruoyi.system.api.domain.dto.biz.business.BusinessDTO;
import com.ruoyi.system.api.domain.dto.order.*;
import com.ruoyi.system.api.domain.dto.order.finance.FinancialVerificationExportDTO;
import com.ruoyi.system.api.domain.dto.system.SysUserListDTO;
import com.ruoyi.system.api.domain.entity.ModelPerson;
import com.ruoyi.system.api.domain.entity.SysUser;
import com.ruoyi.system.api.domain.entity.biz.datastatistics.ModelOrderRankingInfo;
import com.ruoyi.system.api.domain.entity.order.*;
import com.ruoyi.system.api.domain.entity.order.datastatistics.CustomerServiceAddedCompleteCountInfo;
import com.ruoyi.system.api.domain.entity.order.promotion.OrderPromotionDetail;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsDetailVO;
import com.ruoyi.system.api.domain.vo.OrderVideoStatisticsVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountSelectVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessAccountVO;
import com.ruoyi.system.api.domain.vo.biz.business.BusinessVO;
import com.ruoyi.system.api.domain.vo.biz.datastatistics.PieChartVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelInfoVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelOrderSimpleVO;
import com.ruoyi.system.api.domain.vo.biz.model.ModelSelectVO;
import com.ruoyi.system.api.domain.vo.order.*;
import com.ruoyi.system.api.domain.vo.order.datastatistics.OrderVideoBaseBoardVO;
import com.ruoyi.system.api.domain.vo.order.finace.FinancialVerificationAllExportVO;
import com.ruoyi.system.api.domain.vo.order.finace.OrderPayVideoDetailVO;
import com.ruoyi.system.api.domain.vo.order.workbench.PauseMatchVO;
import com.ruoyi.system.api.model.LoginUser;
import com.wnkx.order.config.OrderVideoProperties;
import com.wnkx.order.mapper.OrderInvoiceMapper;
import com.wnkx.order.mapper.OrderMapper;
import com.wnkx.order.mapper.OrderVideoMapper;
import com.wnkx.order.mapper.OrderVideoUploadLinkMapper;
import com.wnkx.order.remote.RemoteService;
import com.wnkx.order.service.*;
import com.wnkx.order.service.core.AsyncTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单_视频Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderVideoServiceImpl extends ServiceImpl<OrderVideoMapper, OrderVideo> implements IOrderVideoService {
    /**
     * 视频内容服务
     */
    private final IOrderVideoContentService videoContentService;
    private final IOrderVideoModelService orderVideoModelService;
    private final IOrderVideoTagService orderVideoTagService;
    private final IOrderVideoCaseService orderVideoCaseService;
    private final AsyncTaskService asyncTaskService;

    private final RemoteService remoteService;
    private final OrderMapper orderMapper;
    private final OrderResourceService orderResourceService;
    private final OrderVideoChangeLogService orderVideoChangeLogService;
    private final OrderVideoFlowRecordService orderVideoFlowRecordService;
    private final OrderVideoModelShippingAddressService orderVideoModelShippingAddressService;
    private final OrderVideoBackModifyAmountRecordsService orderVideoBackModifyAmountRecordsService;
    private final OrderVideoOperateService orderVideoOperateService;
    private final OrderVideoFlowNodeDiagramService orderVideoFlowNodeDiagramService;
    private final OrderVideoModelChangeService orderVideoModelChangeService;
    private final OrderVideoProperties orderVideoProperties;
    private final DelayQueueService delayQueueService;
    private final RedisService redisService;
    private final OrderVideoMatchService orderVideoMatchService;
    private final IOrderVideoMatchPreselectModelService orderVideoMatchPreselectModelService;
    private final OrderInvoiceMapper orderInvoiceMapper;
    private final OrderVideoUploadLinkMapper orderVideoUploadLinkMapper;
    private final OrderPromotionDetailService orderPromotionDetailService;

    private String RECORD_ORDER_INFORMATION_CHANGE_EXCEPTION = "记录订单信息变更异常";

    private final CopyOptions copyOptions = CopyOptions.create()
            .setIgnoreNullValue(true);

    /**
     * 模特数据-模特排单情况
     */
    @Override
    public List<PieChartVO> getModelOrderScheduledData(Collection<Long> modelIds) {
        List<OrderVideo> orderVideos = baseMapper.selectListByShootModelIds(modelIds);
        orderVideos = orderVideos.stream().filter(item -> !OrderStatusEnum.TRADE_CLOSE.getCode().equals(item.getStatus())).collect(Collectors.toList());
        List<PieChartVO> pieChartVOS = new ArrayList<>();
        if (CollUtil.isEmpty(orderVideos)) {
            for (String label : ModelOrderScheduledDataEnum.getLabels()) {
                PieChartVO pieChartVO = new PieChartVO();
                pieChartVO.setLabel(label);
                if (ModelOrderScheduledDataEnum.FIRST_GEAR.getLabel().equals(label)) {
                    pieChartVO.setCount(Convert.toLong(modelIds.size()));
                    pieChartVO.setRatio(BigDecimal.ONE);
                } else {
                    pieChartVO.setCount(0L);
                    pieChartVO.setRatio(BigDecimal.ZERO);
                }
                pieChartVOS.add(pieChartVO);
            }
        } else {
            Map<Long, List<OrderVideo>> orderVideoMap = orderVideos.stream().collect(Collectors.groupingBy(OrderVideo::getShootModelId));

            List<ModelOrderScheduledDataBO> modelOrderScheduledDataBOS = new ArrayList<>();
            for (Long modelId : modelIds) {
                ModelOrderScheduledDataBO modelOrderScheduledDataBO = new ModelOrderScheduledDataBO();
                modelOrderScheduledDataBO.setModelId(modelId);
                modelOrderScheduledDataBO.setCount(Convert.toLong(orderVideoMap.getOrDefault(modelId, Collections.emptyList()).size()));
                modelOrderScheduledDataBOS.add(modelOrderScheduledDataBO);
            }

            for (String label : ModelOrderScheduledDataEnum.getLabels()) {
                PieChartVO pieChartVO = new PieChartVO();

                pieChartVO.setLabel(label);
                List<ModelOrderScheduledDataBO> result = modelOrderScheduledDataBOS.stream().filter(item -> ModelOrderScheduledDataEnum.isInSection(label, BigDecimal.valueOf(item.getCount()))).collect(Collectors.toList());
                pieChartVO.setCount(Convert.toLong(result.size()));
                pieChartVO.setRatio(BigDecimal.valueOf(result.size())
                        .divide(BigDecimal.valueOf(modelOrderScheduledDataBOS.size()), 2, RoundingMode.HALF_UP));
                pieChartVOS.add(pieChartVO);
            }
        }
        return pieChartVOS;
    }

    /**
     * 视频订单列表-获取意向模特下拉框
     */
    @Override
    public List<ModelSelectVO> orderIntentionModelSelect(String keyword) {
        Collection<Long> intentionModelIds = baseMapper.orderIntentionModelSelect();
        if (CollUtil.isEmpty(intentionModelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(intentionModelIds);
        modelListDTO.setName(keyword);
        return remoteService.modelSelect(modelListDTO);
    }

    /**
     * 视频订单数据-基础看板
     */
    @Override
    public OrderVideoBaseBoardVO getOrderVideoBaseBoard() {
        return baseMapper.getOrderVideoBaseBoard();
    }

    /**
     * 视频订单数据统计-按天统计视频订单数据
     */
    @Override
    public OrderVideoDataStatisticsDay getOrderVideoDataStatisticsDay(String date) {
        return baseMapper.getOrderVideoDataStatisticsDay(date);
    }

    /**
     * 通过日期 获取英文部客服新增/完成订单数量
     */
    @Override
    public List<CustomerServiceAddedCompleteCountInfo> getEnglishCustomerServiceOrderCountByDate(String date) {
        return baseMapper.getEnglishCustomerServiceOrderCountByDate(date);
    }

    /**
     * 通过日期 获取中文部客服新增/完成订单数量
     */
    @Override
    public List<CustomerServiceAddedCompleteCountInfo> getChineseCustomerServiceOrderCountByDate(String date) {
        return baseMapper.getChineseCustomerServiceOrderCountByDate(date);
    }

    /**
     * （一次性）初始化模特数据-模特接单排行榜
     */
    @Override
    public Map<String, List<ModelOrderRankingInfo>> getModelOrderRankings(List<String> collect) {
        Map<String, List<ModelOrderRankingInfo>> map = new HashMap<>();

        for (String date : collect) {
            map.put(date, baseMapper.getModelOrderRanking(date));
        }

        return map;
    }

    /**
     * 模特数据统计-模特接单排行榜
     */
    @Override
    public List<ModelOrderRankingInfo> getModelOrderRanking(String date) {
        return baseMapper.getModelOrderRanking(date);
    }

    /**
     * 订单列表-获取订单运营下拉框（运营端）
     */
    @Override
    public List<BusinessAccountSelectVO> backCreateOrderUserNameSelect(String keyword) {
        List<BusinessAccountSelectVO> selectVOS = baseMapper.backCreateOrderUserNameSelect(keyword);
        for (int i = 0; i < selectVOS.size(); i++) {
            BusinessAccountSelectVO selectVO = selectVOS.get(i);
            selectVO.setId(Convert.toLong(i));
        }
        return selectVOS;
    }

    /**
     * 通过商家ID查询商家是否有进行中或者交易完成的视频订单
     */
    @Override
    public Boolean hasValidVideoOrderByBusinessId(Long businessId, String startTime) {
        return baseMapper.hasValidVideoOrderByBusinessId(businessId, startTime);
    }

    /**
     * (一次性)更新order_video_model_change数据
     */
    @Override
    public List<OrderVideoModelChange> test3() {
        return baseMapper.test3();
    }

    /**
     * （一次性）补全order_video_match的选定信息
     */
    @Override
    public List<OrderVideoMatch> test2() {
        return baseMapper.test2();
    }

    /**
     * （一次性）补全order_video_model_change的选定信息
     */
    @Override
    public List<OrderVideoModelChange> test() {
        return baseMapper.test();
    }

    /**
     * 通过视频编码获取视频订单列表
     */
    @Override
    public List<OrderVideo> selectListByVideoCodes(List<String> videoCodes) {
        return baseMapper.selectListByVideoCodes(videoCodes);
    }

    @Override
    public OrderVideo selectOneByVideoCode(String videoCode) {
        return baseMapper.selectOneByVideoCode(videoCode);
    }

    /**
     * 更新视频订单 若字段为null 更新为null
     * PS:请注意字段值
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderVideoBatchFieldNullToNull(List<OrderVideo> orderVideos) {
        for (OrderVideo orderVideo : orderVideos) {
            baseMapper.updateOrderVideoFieldNullToNull(orderVideo);
        }
    }

    /**
     * 更新视频订单 若字段为null 更新为null
     * PS:请注意字段值
     */
    @Override
    public void updateOrderVideoFieldNullToNull(OrderVideo orderVideo) {
        baseMapper.updateOrderVideoFieldNullToNull(orderVideo);
    }

    /**
     * 模特变更对接客服更新视频订单的出单人ID
     */
    @Override
    public void updateIssueId(UpdateIssueIdDTO dto) {
        if (CollUtil.isEmpty(dto.getModelIds())) {
            return;
        }
        baseMapper.updateIssueId(dto);
    }

    @Override
    public void setBalanceZeroByOrderNum(List<String> orderNums) {
        baseMapper.setBalanceZeroByOrderNum(orderNums);
    }

    /**
     * 运营手动获取产品图
     */
    @Override
    public void crawlProductPic(Long videoId) {
        OrderVideo orderVideo = baseMapper.selectById(videoId);
        Assert.notNull(orderVideo, "订单不存在");
        Assert.isTrue(CharSequenceUtil.isBlank(orderVideo.getProductPic()), "订单已有产品图，请刷新列表~");
        Assert.isTrue(PlatformEnum.AMAZON.getCode().equals(orderVideo.getPlatform()), "当前平台不是亚马逊，无法爬取产品图");
        Assert.isTrue(CharSequenceUtil.isNotBlank(orderVideo.getProductLink()), "产品链接为空，无法爬取产品图");
        Assert.isTrue(CharSequenceUtil.startWith(orderVideo.getProductLink(), "https://www.amazon.com"), "产品链接不是亚马逊链接，无法爬取产品图");

        if (!redisService.getLock(CacheConstants.CRAWL_PRODUCT_PIC_KEY + videoId, 30L)) {
            throw new ServiceException("正在爬取图片，请勿频繁操作~");
        }

        AsyncCrawlProductPicDTO dto = new AsyncCrawlProductPicDTO();
        dto.setId(orderVideo.getId());
        dto.setProductLink(orderVideo.getProductLink());

        AsyncCrawlTask asyncCrawlTask = new AsyncCrawlTask();
        asyncCrawlTask.setType(OrderTypeEnum.VIDEO_ORDER.getCode());
        asyncCrawlTask.setAsyncCrawlProductPic(List.of(dto));
        remoteService.asyncUpdateOrderVideoImage(asyncCrawlTask);
    }

    /**
     * 订单列表-获取下单用户下拉框（商家端）
     */
    @Override
    public Set<String> companyOrderUserSelect(String keyword) {
        Long businessId = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId();
        List<OrderVideo> companyOrderUser = baseMapper.companyOrderUserSelect(businessId, keyword);
        return companyOrderUser.stream().map(OrderVideo::getCreateOrderUserName).collect(Collectors.toSet());
    }

    /**
     * 获取待完成、需确认状态下的订单的出单人信息
     */
    @Override
    public List<UnFinishedAndNeedConfirmOrderIssueSelectVO> unFinishedAndNeedConfirmOrderIssueSelect() {
        List<OrderVideo> orderVideos = baseMapper.getUnFinishedAndNeedConfirmOrderIssueId();
        if (CollUtil.isEmpty(orderVideos)) {
            return Collections.emptyList();
        }

        Set<Long> issueIds = orderVideos.stream().map(OrderVideo::getIssueId).collect(Collectors.toSet());
        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(issueIds);
        Map<Long, UserVO> userMap = remoteService.getUserMap(dto);

        return orderVideos.stream().map(item -> {
            UnFinishedAndNeedConfirmOrderIssueSelectVO unFinishedAndNeedConfirmOrderIssueSelectVO = BeanUtil.copyProperties(item, UnFinishedAndNeedConfirmOrderIssueSelectVO.class);
            unFinishedAndNeedConfirmOrderIssueSelectVO.setUserVO(userMap.get(item.getIssueId()));
            return unFinishedAndNeedConfirmOrderIssueSelectVO;
        }).collect(Collectors.toList());
    }

    /**
     * 删除视频订单自动完成时间
     */
    @Override
    public void removeAutoCompleteTime(Long videoId) {
        baseMapper.setAutoCompleteTime(videoId, null);
        delayQueueService.removeTask(DelayQueueConstant.ORDER_AUTO_FINISHED_QUEUE_NAME, videoId.toString());
    }

    /**
     * 设置视频订单自动完成时间
     */
    @Override
    public void setAutoCompleteTime(Long videoId) {
        OrderVideo byId = getById(videoId);
        if (ObjectUtil.isNotNull(byId.getAutoCompleteTime())) {
            return;
        }
        baseMapper.setAutoCompleteTime(videoId, DateUtil.offset(DateUtil.date(), DateField.HOUR_OF_DAY, orderVideoProperties.getOrderAutoFinishedTime()));
        delayQueueService.addTaskIfAbsent(DelayQueueConstant.ORDER_AUTO_FINISHED_QUEUE_NAME, videoId.toString(), orderVideoProperties.getOrderAutoFinishedTime(), TimeUnit.HOURS);
    }

    /**
     * 释放视频订单到模特公池
     */
    @Override
    public void releaseOrderVideo(Collection<Long> videoIds) {
        List<CountVideoOutModelVO> countVideoOutModelVOS = orderVideoMatchService.countVideoOutModel(videoIds);
        if (CollUtil.isEmpty(countVideoOutModelVOS)) {
            return;
        }
        List<OrderVideo> orderVideos = listByIds(videoIds);
        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }
        Map<Long, OrderVideo> videoMap = orderVideos.stream().collect(Collectors.toMap(OrderVideo::getId, orderVideo -> orderVideo));

        for (CountVideoOutModelVO countVideoOutModelVO : countVideoOutModelVOS) {
            OrderVideo orderVideo = videoMap.get(countVideoOutModelVO.getVideoId());
            if (ObjectUtil.isNull(orderVideo)) {
                continue;
            }
            if (ObjectUtil.isNull(countVideoOutModelVO.getCount()) || countVideoOutModelVO.getCount() == 0) {
                //  首次淘汰 设置视频订单的释放时间
                orderVideo.setReleaseTime(DateUtil.date());
            } else if (ObjectUtil.isNotNull(orderVideo.getReleaseTime()) && FlagEnum.NO_FLAG.getCode().equals(orderVideo.getReleaseFlag())) {
                orderVideo.setReleaseFlag(FlagEnum.FLAG.getCode());
            }
        }
        baseMapper.updateBatchById(orderVideos);
    }

    /**
     * 释放视频订单到模特公池
     */
    @Override
    public void releaseOrderVideo(Long videoId) {
        Long countVideoOutModel = orderVideoMatchService.countVideoOutModel(videoId);
        OrderVideo orderVideo = getById(videoId);
        if (ObjectUtil.isNull(countVideoOutModel) || countVideoOutModel == 0) {
            //  首次淘汰 设置视频订单的释放时间
            orderVideo.setReleaseTime(DateUtil.date());
        } else if (ObjectUtil.isNotNull(orderVideo.getReleaseTime()) && FlagEnum.NO_FLAG.getCode().equals(orderVideo.getReleaseFlag())) {
            orderVideo.setReleaseFlag(FlagEnum.FLAG.getCode());
        }
        baseMapper.updateById(orderVideo);
    }

    /**
     * 视频订单发货信息
     */
    @Override
    public ShippingVO shippingInfo(Long videoId) {
        OrderVideo orderVideo = baseMapper.selectById(videoId);
        Assert.notNull(orderVideo, "视频订单不存在");
        checkVideoStatus(orderVideo, OrderStatusEnum.NEED_FILLED);
        Assert.isTrue(IsObjectEnum.OBJECT.getCode().equals(orderVideo.getIsObject()), "该视频订单无需发货");

        OrderVideoModelShippingAddressVO orderVideoModelShippingAddress = orderVideoModelShippingAddressService.getLastOrderVideoModelShippingAddressByVideoId(videoId, orderVideo.getRollbackId());
        Assert.notNull(orderVideoModelShippingAddress, "模特收件信息不能为空");

        BusinessVO businessVo = remoteService.getBusinessVo(BusinessDTO.builder().id(orderVideo.getCreateOrderBusinessId()).build());
        if (StatusTypeEnum.NO.getCode().equals(businessVo.getPhoneVisible())) {
            orderVideoModelShippingAddress.setPhone(null);
            orderVideoModelShippingAddress.getShootModel().setPhone(null);
        }

        ShippingVO shippingVO = new ShippingVO();
        shippingVO.setId(orderVideo.getId());
        shippingVO.setVideoCode(orderVideo.getVideoCode());
        shippingVO.setProductChinese(orderVideo.getProductChinese());
        shippingVO.setProductEnglish(orderVideo.getProductEnglish());
        shippingVO.setProductLink(orderVideo.getProductLink());
        shippingVO.setProductPic(orderVideo.getProductPic());

        BeanUtil.copyProperties(orderVideoModelShippingAddress.getShootModel(), shippingVO, "id");

        shippingVO.setShippingRemark(orderVideoModelShippingAddress.getShippingRemark());
        shippingVO.setShippingPics(orderVideoModelShippingAddress.getShippingPics());
        shippingVO.setLogisticFlag(orderVideoModelShippingAddress.getLogisticFlag());
        shippingVO.setLogisticFlagRemark(orderVideoModelShippingAddress.getLogisticFlagRemark());
        shippingVO.setLogisticFlagTime(orderVideoModelShippingAddress.getLogisticFlagTime());

        shippingVO.setChange(compareModelShippingAddress(orderVideoModelShippingAddress.getShootModel(), orderVideoModelShippingAddress.getRecipient(), orderVideoModelShippingAddress.getDetailAddress(),
                orderVideoModelShippingAddress.getCity(), orderVideoModelShippingAddress.getState(), orderVideoModelShippingAddress.getZipcode(),
                orderVideoModelShippingAddress.getNation(), orderVideoModelShippingAddress.getPhone()));

        return shippingVO;
    }

    /**
     * 接收抓取亚马逊图片更新视频订单
     */
    @Override
    public void updateBatchOrderVideoProductPic(List<UpdateBatchOrderVideoProductPicDTO> dto) {
        List<OrderVideo> orderVideos = BeanUtil.copyToList(dto, OrderVideo.class);
        Map<String, String> map = UpdateBatchOrderVideoProductPicDTO.getMap(dto);

        Set<String> productLinks = dto.stream().map(UpdateBatchOrderVideoProductPicDTO::getProductLink).collect(Collectors.toSet());
        List<OrderVideo> productPicIsNullList = baseMapper.selectListByProductPicIsNullAndProductLink(productLinks);
        if (CollUtil.isNotEmpty(productPicIsNullList)) {
            for (OrderVideo orderVideo : productPicIsNullList) {
                orderVideo.setProductPic(map.get(orderVideo.getProductLink()));
            }
            orderVideos.addAll(productPicIsNullList);
        }
        updateBatchById(orderVideos);
    }

    /**
     * 订单列表-获取下单用户下拉框（运营端）
     */
    @Override
    public List<BusinessAccountSelectVO> backOrderUserSelect(String keyword) {
        Set<Long> orderUserId = baseMapper.getOrderUserId();
        if (CollUtil.isEmpty(orderUserId)) {
            return Collections.emptyList();
        }

        BusinessAccountDetailDTO businessAccountDetailDTO = new BusinessAccountDetailDTO();
        businessAccountDetailDTO.setIds(orderUserId);
        businessAccountDetailDTO.setNickName(keyword);
        return remoteService.businessAccountSelect(businessAccountDetailDTO);
    }

    @Override
    public List<ModelOrderVO> getModelOrderCountCopy(Collection<Long> modelIds) {
        return baseMapper.getModelOrderCount(modelIds);
    }

    /**
     * 获取模特待拍数、已完成订单数、超时订单
     */
    @Override
    public List<ModelOrderVO> getModelOrderCount(Collection<Long> modelIds) {
        //  获取模特 待拍数、待确认数、待确认订单数（即运营提交预选模特给商家后 待商家确认的订单数（即需发货订单））、已完成订单数
        List<ModelOrderVO> modelOrderVOS = baseMapper.getModelOrderCount(modelIds);

        //  获取逾期未反馈素材的模特
        List<Long> overdueModelId = orderVideoModelService.checkModelOverdueVideo(modelIds);

        //  获取模特剩余需携带订单数
        List<ModelCarryVO> carryCounts = orderVideoMatchService.getModelLeftCarryCountByModelIds(modelIds);
        Map<Long, Long> leftCarryCountMap = new HashMap<>();
        for (ModelCarryVO mainCarryCount : carryCounts) {
            leftCarryCountMap.put(mainCarryCount.getModelId(), mainCarryCount.getLeftCarryCount());
        }

        for (ModelOrderVO modelOrderVO : modelOrderVOS) {
            if (overdueModelId.contains(modelOrderVO.getModelId())) {
                modelOrderVO.setHasTimeout(true);
            }
            modelOrderVO.setCarryCount(leftCarryCountMap.get(modelOrderVO.getModelId()));
        }

        return modelOrderVOS;
    }

    /**
     * 批量更新视频订单的对接人
     *
     * @param orderNums 订单编号
     * @param contactId 对接人客服id
     */
    @Override
    public void updateOrderVideoContact(List<String> orderNums, Long contactId) {
        List<OrderVideo> orderVideos = baseMapper.selectUnFinishOrderByOrderNums(orderNums);
        if (CollUtil.isEmpty(orderVideos)) {
            return;
        }

        List<OrderVideo> updateOrderVideos = new ArrayList<>();
        for (OrderVideo orderVideo : orderVideos) {
            if (orderVideo.getContactId() != null && orderVideo.getContactId().compareTo(contactId) == 0) {
                continue;
            }
            orderVideo.setContactId(contactId);
            updateOrderVideos.add(orderVideo);
        }
        if (CollUtil.isEmpty(updateOrderVideos)) {
            return;
        }
        updateBatchById(updateOrderVideos);
    }

    /**
     * 校验商家数据权限
     *
     * @return true 校验不通过 false 校验通过
     */
    @Override
    public Boolean checkUserDataScope(CheckDataScopeDTO dto, BusinessAccountVO businessAccount) {
        return baseMapper.checkUserDataScope(dto, businessAccount) >= dto.getVideoIds().size();
    }

    /**
     * 校验运营数据权限
     *
     * @return true 校验不通过 false 校验通过
     */
    @Override
    public Boolean checkManagerDataScope(CheckDataScopeDTO dto, Long userid) {
        return baseMapper.checkManagerDataScope(dto, userid) > 0;
    }

    /**
     * 校验admin数据权限
     *
     * @return true 校验通过 false 校验不通过
     */
    @Override
    public Boolean checkAdminDataScope(List<Long> videoIds) {
        return baseMapper.checkAdminDataScope(videoIds);
    }

    /**
     * 运营根据匹配情况修改订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void operationVideoCase(OrderOperationVideoCaseDTO dto) {
        OrderVideoCase orderVideoCase = orderVideoCaseService.getByIdAndVideoId(dto.getCaseId(), dto.getId());
        Assert.notNull(orderVideoCase, "匹配反馈不存在！");
        Assert.isTrue(ReplyEnum.AGREE.getCode().equals(orderVideoCase.getReplyContent()), "商家不同意反馈，您无法修改！");
        Assert.isTrue(FlagEnum.NO_FLAG.getCode().equals(orderVideoCase.getOperateEdit()), "只允许修改一次，请勿重复操作！");

        orderVideoCase.setOperateEdit(FlagEnum.FLAG.getCode());
        orderVideoCaseService.updateById(orderVideoCase);

        OrderVideoCurrentInfoVO orderVideoCurrentInfo = getOrderVideoCurrentInfo(dto.getId());

        OrderVideo orderVideo = getById(dto.getId());
        BeanUtil.copyProperties(dto, orderVideo);
        orderVideo.setLastChangeTime(DateUtil.date());
        if (ObjectUtil.isNotNull(dto.getOrderVideoCautionsDTO()) && CollUtil.isNotEmpty(dto.getOrderVideoCautionsDTO().getCautionsPics())) {
            List<Long> resourceIds = orderResourceService.saveBatchOrderResource(dto.getOrderVideoCautionsDTO().getCautionsPics());
            orderVideo.setCautionsPicId(StrUtil.join(StrUtil.COMMA, resourceIds));
        }
        this.updateById(orderVideo);

        dto.init();
        videoContentService.saveBatchVideoContent(List.of(orderVideo.getId()), List.of(VideoContentTypeEnum.REQUIRE.getCode(), VideoContentTypeEnum.CAUTIONS.getCode()), dto.getOrderVideoContents(), StatusTypeEnum.NO.getCode());

        try {
            OrderVideoCurrentInfoVO orderVideoLastInfo = BeanUtil.copyProperties(dto, OrderVideoCurrentInfoVO.class);
            if (ObjectUtil.isNotNull(dto.getOrderVideoCautionsDTO())) {
                orderVideoLastInfo.setCautions(dto.getOrderVideoCautionsDTO().getCautions());
                orderVideoLastInfo.setCautionsPics(dto.getOrderVideoCautionsDTO().getCautionsPics());
            }
            Map<String, Object> differencesMap = ObjectUtils.compareAndReturnDifferences(orderVideoCurrentInfo, orderVideoLastInfo, "shootingCountry", "modelType", "shootRequired", "cautions", "cautionsPics");
            if (CollUtil.isEmpty(differencesMap)) {
                return;
            }
            OrderVideoChangeLogDTO orderVideoChangeLogDTO = OrderVideoChangeLogDTO.builder().videoId(dto.getId()).rollbackId(orderVideo.getRollbackId()).logType(ChangeLogTypeEnum.AGREE_EDIT_LOG.getCode()).data(differencesMap).build();
            orderVideoChangeLogService.addVideoChangeLog(orderVideoChangeLogDTO);
        } catch (Exception e) {
            log.error(RECORD_ORDER_INFORMATION_CHANGE_EXCEPTION, e);
            throw new ServiceException(RECORD_ORDER_INFORMATION_CHANGE_EXCEPTION);
        }
    }

    /**
     * 订单列表-获取出单人下拉框
     */
    @Override
    public List<UserVO> orderIssueSelect(String keyword) {
        Set<Long> orderIssueId = baseMapper.getOrderIssueId();
        if (CollUtil.isEmpty(orderIssueId)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(orderIssueId);
        dto.setUserName(keyword);
        return remoteService.customerServiceSelect(dto);
    }

    /**
     * 订单列表-获取对接人下拉框
     */
    @Override
    public List<UserVO> orderContactSelect(String keyword) {
        Set<Long> orderContactId = baseMapper.getOrderContactId();
        if (CollUtil.isEmpty(orderContactId)) {
            return Collections.emptyList();
        }

        SysUserListDTO dto = new SysUserListDTO();
        dto.setUserId(orderContactId);
        dto.setUserName(keyword);
        return remoteService.customerServiceSelect(dto);
    }

    /**
     * 订单列表-获取拍摄模特下拉框
     */
    @Override
    public List<ModelSelectVO> orderShootModelSelect(String keyword) {
        Set<Long> shootModelIds = baseMapper.getOrderShootModelId();
        if (CollUtil.isEmpty(shootModelIds)) {
            return Collections.emptyList();
        }

        ModelListDTO modelListDTO = new ModelListDTO();
        modelListDTO.setId(shootModelIds);
        modelListDTO.setName(keyword);
        return remoteService.modelSelect(modelListDTO);
    }

    /**
     * 根据条件查询视频订单列表
     */
    @Override
    public List<OrderVideo> selectOrderVideoListByConditionV2(OrderVideoListDTO orderVideoListDTO) {
        return baseMapper.selectOrderVideoListByConditionV2(orderVideoListDTO);
    }

    /**
     * 获取商户视频订单数量
     */
    @Override
    public int getOrderVideoCount(String merchantCode) {
        List<Integer> ignoreStatus = new ArrayList<>();
        ignoreStatus.add(OrderStatusEnum.UN_PAY.getCode());
        ignoreStatus.add(OrderStatusEnum.UN_CHECK.getCode());
        int orderVideoCount = baseMapper.getOrderVideoCount(merchantCode, ignoreStatus);
        return orderVideoCount == 0 ? 1 : orderVideoCount + 1;
    }

    @Override
    public int getOrderVideoCareCount(String merchantCode) {
        return baseMapper.getOrderVideoCareCount(merchantCode);
    }

    /**
     * 检查视频订单状态
     *
     * @param orderVideos 视频订单
     * @param statusEnum  应该是什么状态
     */
    @Override
    public void checkVideoStatus(List<OrderVideo> orderVideos, OrderStatusEnum... statusEnum) {
        List<Integer> status = Arrays.stream(statusEnum).map(OrderStatusEnum::getCode).collect(Collectors.toList());
        for (OrderVideo orderVideo : orderVideos) {
            if (!status.contains(orderVideo.getStatus())) {
                log.error(StrUtil.format("视频订单状态异常，需要是：{}，当前是：[{}]，请刷新页面",
                        Arrays.stream(statusEnum).map(OrderStatusEnum::getLabel).collect(Collectors.toList()),
                        OrderStatusEnum.getLabel(orderVideo.getStatus())));
                throw new ServiceException("订单状态发生变化，请刷新页面重试~");
            }
        }
    }

    /**
     * 检查视频订单状态
     *
     * @param orderVideo 视频订单
     * @param statusEnum 应该是什么状态
     */
    @Override
    public void checkVideoStatus(OrderVideo orderVideo, OrderStatusEnum... statusEnum) {
        checkVideoStatus(List.of(orderVideo), statusEnum);
    }

    /**
     * 通过订单编号获取视频订单
     */
    @Override
    public List<OrderVideo> selectByOrderNum(String orderNum) {
        return baseMapper.selectByOrderNum(orderNum);
    }

    @Override
    public List<OrderPayVideoDetailVO> orderPayDetailVideoList(OrderPayDetailVideoListDTO dto) {
        return baseMapper.orderPayDetailVideoList(dto);
    }

    @Override
    public List<FinancialVerificationAllExportVO> financialVerificationAllExportList(FinancialVerificationExportDTO dto) {
        return baseMapper.financialVerificationAllExportList(dto);
    }

    @Override
    public List<OrderVideo> selectByOrderNums(Collection<String> orderNums) {
        if (CollUtil.isEmpty(orderNums)) {
            return new ArrayList<>();
        }
        return baseMapper.selectByOrderNums(orderNums);
    }

    /**
     * 通过订单编号获取 非交易关闭状态数据
     */
    @Override
    public List<OrderVideo> selectValidByOrderNums(List<String> orderNums) {
        if (CollUtil.isEmpty(orderNums)) {
            return Collections.emptyList();
        }
        List<OrderVideo> orderVideos = baseMapper.selectByOrderNums(orderNums);
        return orderVideos.stream().filter(item -> !OrderStatusEnum.TRADE_CLOSE.getCode().equals(item.getStatus())).collect(Collectors.toList());
    }

    @Override
    public List<OrderVideo> selectValidByOrderNumsAsc(List<String> orderNums) {
        if (CollUtil.isEmpty(orderNums)) {
            return Collections.emptyList();
        }
        List<OrderVideo> orderVideos = baseMapper.selectByOrderNumsAsc(orderNums);
        return orderVideos.stream().filter(item -> !OrderStatusEnum.TRADE_CLOSE.getCode().equals(item.getStatus())).collect(Collectors.toList());
    }

    @Override
    public List<OrderVideo> selectValidByOrderNum(String orderNum) {
        List<OrderVideo> orderVideos = baseMapper.selectByOrderNum(orderNum);
        if (CollUtil.isEmpty(orderVideos)) {
            return Collections.emptyList();
        }

        return orderVideos.stream().filter(item -> !OrderStatusEnum.TRADE_CLOSE.getCode().equals(item.getStatus())).collect(Collectors.toList());
    }

    /**
     * 上传产品图
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void uploadProductImage(Long videoId, String productPic) {
        OrderVideo orderVideo = baseMapper.selectById(videoId);
        checkVideoStatus(orderVideo, OrderStatusEnum.UN_CONFIRM, OrderStatusEnum.UN_MATCH);
        orderVideo.setProductPic(productPic);
        orderVideo.setLastChangeTime(DateUtil.date());
        baseMapper.updateById(orderVideo);

        OrderVideoChangeLogDTO orderVideoChangeLogDTO = new OrderVideoChangeLogDTO();
        orderVideoChangeLogDTO.setVideoId(videoId);
        orderVideoChangeLogDTO.setRollbackId(orderVideo.getRollbackId());
        orderVideoChangeLogDTO.setLogType(ChangeLogTypeEnum.AUDIT_EDIT_LOG.getCode());
        orderVideoChangeLogDTO.setData(Map.of("productPic", productPic));
        orderVideoChangeLogService.addVideoChangeLog(orderVideoChangeLogDTO);
    }

    @Override
    public OrderVideoStatisticsVO orderVideoStatistics(OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
//        orderVideoStatisticsDTO.setStatusList(OrderStatusEnum.getOrderUnFinishStatusList());
        OrderVideoStatisticsVO orderVideoStatisticsVO = baseMapper.orderVideoStatistics(orderVideoStatisticsDTO);
        if (StringUtils.isNull(orderVideoStatisticsVO) || StringUtils.isNull(orderVideoStatisticsVO.getOrderVideoTotal())) {
            orderVideoStatisticsVO = new OrderVideoStatisticsVO();
            orderVideoStatisticsVO.setOrderVideoTotal(0);
            orderVideoStatisticsVO.setRecentOrderTotal(0);
            orderVideoStatisticsVO.setPreFinishOrderTotal(0);
            return orderVideoStatisticsVO;
        }
        Date endOfToday = DateUtils.getEndOfToday();
        orderVideoStatisticsDTO.setTimeBegin(DateUtils.addDays(endOfToday, -30));
        orderVideoStatisticsDTO.setTimeEnd(endOfToday);
        OrderVideoStatisticsVO recentOrderTotal = baseMapper.orderVideoStatistics(orderVideoStatisticsDTO);
        if (StringUtils.isNull(recentOrderTotal) || StringUtils.isNull(recentOrderTotal.getOrderVideoTotal())) {
            orderVideoStatisticsVO.setRecentOrderTotal(0);
            return orderVideoStatisticsVO;
        }
        orderVideoStatisticsVO.setRecentOrderTotal(recentOrderTotal.getOrderVideoTotal());
        return orderVideoStatisticsVO;
    }

    @Override
    public List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetail(OrderVideoStatisticsDTO orderVideoStatisticsDTO) {
        List<OrderVideoStatisticsDetailVO> orderVideoStatisticsDetailVOS = baseMapper.orderVideoStatisticsDetail(orderVideoStatisticsDTO);
        if (StringUtils.isEmpty(orderVideoStatisticsDetailVOS)) {
            return new ArrayList<>();
        }
        Date endOfToday = DateUtils.getEndOfToday();
        orderVideoStatisticsDTO.setTimeBegin(DateUtils.addDays(endOfToday, -30));
        orderVideoStatisticsDTO.setTimeEnd(endOfToday);
        List<OrderVideoStatisticsDetailVO> recentOrderTotal = baseMapper.orderVideoStatisticsDetail(orderVideoStatisticsDTO);
        Map<Long, Integer> orderMap = orderVideoStatisticsDetailVOS.stream().collect(Collectors.toMap(OrderVideoStatisticsDetailVO::getMerchantId, OrderVideoStatisticsDetailVO::getOrderVideoTotal));
        Map<Long, Integer> recentOrderMap = new HashMap<>();
        if (StringUtils.isNotEmpty(recentOrderTotal)) {
            recentOrderMap = recentOrderTotal.stream().collect(Collectors.toMap(OrderVideoStatisticsDetailVO::getMerchantId, OrderVideoStatisticsDetailVO::getOrderVideoTotal));
        }

        for (OrderVideoStatisticsDetailVO item : orderVideoStatisticsDetailVOS) {
            item.setOrderVideoTotal(StringUtils.isNull(orderMap.get(item.getMerchantId())) ? 0 : orderMap.get(item.getMerchantId()));
            item.setRecentOrderTotal(StringUtils.isNull(recentOrderMap.get(item.getMerchantId())) ? 0 : recentOrderMap.get(item.getMerchantId()));
            item.setAfterSaleRate(item.getAfterSaleRate().multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        }
        return orderVideoStatisticsDetailVOS;
    }

    /**
     * 获取可退款金额
     */
    @Override
    public BigDecimal getRefundAmount(Long videoId) {
        return baseMapper.getRefundAmount(videoId);
    }

    /**
     * 修改视频订单_获取视频详细信息
     *
     * @param id 视频订单的主键
     */
    @Override
    public OrderVideoVO getOrderVideoInfo(Long id) {
        OrderVideo orderVideo = getById(id);
        if (ObjectUtil.isNull(orderVideo)) {
            return null;
        }

        OrderVideoVO orderVideoVO = BeanUtil.copyProperties(orderVideo, OrderVideoVO.class);
        if (ObjectUtil.isNotNull(orderVideo.getPicCount())){
            orderVideoVO.setSurplusPicCount(PicCountEnum.getValue(orderVideo.getPicCount()) - Optional.ofNullable(orderVideo.getRefundPicCount()).orElse(0));
        }else {
            orderVideoVO.setSurplusPicCount(0);
        }
        if (UserTypeConstants.MANAGER_TYPE == SecurityUtils.getLoginUserType()) {
            assembleBackOrderVideoInfo(orderVideoVO);
        } else {
            assembleCompanyOrderVideoInfo(orderVideoVO);
        }

        return orderVideoVO;
    }

    /**
     * 组装运营端视频订单详情回显数据
     *
     * @param orderVideoVO 视频订单返回VO
     */
    private void assembleBackOrderVideoInfo(OrderVideoVO orderVideoVO) {
        assembleOrderVideoInfo(orderVideoVO);

        List<OrderVideoContent> orderVideoContents = videoContentService.selectListByVideoIdOrTypes(orderVideoVO.getId(), CollUtil.toList(VideoContentTypeEnum.REQUIRE.getCode()
                , VideoContentTypeEnum.CAUTIONS.getCode(), VideoContentTypeEnum.CLIPS_REQUIRED.getCode()
                , VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode(), VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode(), VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode()
        ));

        List<OrderVideoContent> shootRequired = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
        List<OrderVideoContent> cautions = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CAUTIONS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
        List<OrderVideoContent> clipsRequired = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CLIPS_REQUIRED.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
        List<OrderVideoContent> orderSpecificationRequire = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
        List<OrderVideoContent> particularEmphasis = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
        OrderVideoContent sellingPointProduct = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).findFirst().orElse(new OrderVideoContent());

        List<Long> resourceIds = new ArrayList<>();
        if (StrUtil.isNotBlank(orderVideoVO.getCautionsPicId())) {
            resourceIds.addAll(StringUtils.splitToLong(orderVideoVO.getCautionsPicId(), StrUtil.COMMA));
        }
        if (StrUtil.isNotEmpty(orderVideoVO.getParticularEmphasisPicIds())) {
            resourceIds.addAll(StringUtils.splitToLong(orderVideoVO.getParticularEmphasisPicIds(), StrUtil.COMMA));
        }
        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);
        OrderVideoCautionsVO orderVideoCautionsVO = new OrderVideoCautionsVO();
        if (StrUtil.isNotBlank(orderVideoVO.getCautionsPicId())) {
            for (Long cautionsPicId : StringUtils.splitToLong(orderVideoVO.getCautionsPicId(), StrUtil.COMMA)) {
                orderVideoCautionsVO.getCautionsPics().add(resourceMap.getOrDefault(cautionsPicId, new OrderResource()).getObjectKey());
            }
        }
        List<String> particularEmphasisPic = new ArrayList<>();
        if (StrUtil.isNotBlank(orderVideoVO.getParticularEmphasisPicIds())) {
            for (Long particularEmphasisPicId : StringUtils.splitToLong(orderVideoVO.getParticularEmphasisPicIds(), StrUtil.COMMA)) {
                particularEmphasisPic.add(resourceMap.getOrDefault(particularEmphasisPicId, new OrderResource()).getObjectKey());
            }
        }

        orderVideoVO.setParticularEmphasisPic(particularEmphasisPic);
        orderVideoCautionsVO.setCautions(cautions);
        orderVideoVO.setShootRequired(shootRequired);
        orderVideoVO.setSellingPointProduct(sellingPointProduct.getContent());
        orderVideoVO.setOrderVideoCautionsVO(orderVideoCautionsVO);
        orderVideoVO.setClipsRequired(clipsRequired);
        orderVideoVO.setOrderSpecificationRequire(CollUtil.isNotEmpty(orderSpecificationRequire) ? orderSpecificationRequire.get(0).getContent() : null);
        orderVideoVO.setParticularEmphasis(CollUtil.isNotEmpty(particularEmphasis) ? particularEmphasis.get(0).getContent() : null);

        orderVideoVO.setHasChangeLog(orderVideoChangeLogService.videoHasChangeLog(orderVideoVO.getId()));
        orderVideoVO.setOrderDiscountDetailVOS(orderPromotionDetailService.selectOrderDiscountDetailsByVideoId(orderVideoVO.getId()));

        List<OrderVideoRefundSimpleVO> orderVideoRefundVOS = SpringUtils.getBean(IOrderVideoRefundService.class).selectOrderVideoRefundSimpleListByVideoId(Collections.singletonList(orderVideoVO.getId()));
        if (CollUtil.isNotEmpty(orderVideoRefundVOS)) {
            orderVideoVO.setOrderVideoRefund(orderVideoRefundVOS.get(0));
        }
    }

    /**
     * 通过订单编号批量更新视频订单状态
     *
     * @param orderNums 订单编号
     * @param status    更新的状态
     */
    @Override
    public void updateStatusByOrderNum(List<String> orderNums, Integer status) {
        baseMapper.updateStatusByOrderNum(orderNums, status);
    }

    /**
     * 审核订单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderVO editOperationOrderVideoInfo(OrderOperationVideoDTO orderOperationVideoDTO) {
        OrderVideo orderVideo = getById(orderOperationVideoDTO.getId());
        checkVideoStatus(orderVideo, OrderStatusEnum.UN_CONFIRM);
        backUserEditOrderVideo(orderOperationVideoDTO, orderVideo, ChangeLogTypeEnum.AUDIT_EDIT_LOG);
        SpringUtils.getBean(IOrderService.class).createOrderFlow(orderVideo, OrderStatusEnum.UN_MATCH, "编辑订单");
        return new CreateOrderVO();
    }

    /**
     * 获取视频订单当前信息（用于比较变更后的数据）
     */
    @Override
    public OrderVideoCurrentInfoVO getOrderVideoCurrentInfo(Long id) {
        OrderVideoCurrentInfoVO infoVO = baseMapper.getOrderVideoCurrentInfo(id);
        OrderVideo orderVideo = baseMapper.selectById(id);

        List<Long> resourceIds = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(orderVideo.getCautionsPicId())) {
            resourceIds.addAll(StringUtils.splitToLong(orderVideo.getCautionsPicId(), StrUtil.COMMA));
        }

        List<OrderVideoContent> orderVideoContents = videoContentService.selectListByVideoIdOrTypes(id, CollUtil.toList(VideoContentTypeEnum.REQUIRE.getCode(), VideoContentTypeEnum.CAUTIONS.getCode(), VideoContentTypeEnum.CLIPS_REQUIRED.getCode()
                , VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode(), VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode(), VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode()));
        List<VideoContentDTO> shootRequired = BeanUtil.copyToList(orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList()), VideoContentDTO.class);
        List<VideoContentDTO> cautions = BeanUtil.copyToList(orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CAUTIONS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList()), VideoContentDTO.class);
        List<VideoContentDTO> clipsRequired = BeanUtil.copyToList(orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.CLIPS_REQUIRED.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList()), VideoContentDTO.class);
        List<VideoContentDTO> sellingPointProduct = BeanUtil.copyToList(orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList()), VideoContentDTO.class);
        List<OrderVideoContent> orderSpecificationRequire = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());
        List<OrderVideoContent> particularEmphasis = orderVideoContents.stream().filter(vc -> vc.getType().equals(VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode())).sorted(Comparator.comparingInt(OrderVideoContent::getSort).thenComparing(OrderVideoContent::getCreateTime)).collect(Collectors.toList());

        if (CharSequenceUtil.isNotBlank(orderVideo.getCautionsPicId())) {
            List<OrderResource> orderResources = orderResourceService.selectListByIds(StringUtils.splitToLong(orderVideo.getCautionsPicId(), StrUtil.COMMA));
            infoVO.setCautionsPics(orderResources.stream().map(OrderResource::getObjectKey).collect(Collectors.toList()));
        } else {
            infoVO.setCautionsPics(Collections.emptyList());
        }

        infoVO.setShootRequired(shootRequired);
        infoVO.setCautions(cautions);
        infoVO.setClipsRequired(clipsRequired);
        infoVO.setOrderSpecificationRequire(CollUtil.isNotEmpty(orderSpecificationRequire) ? orderSpecificationRequire.get(0).getContent() : "");
        infoVO.setParticularEmphasis(CollUtil.isNotEmpty(particularEmphasis) ? particularEmphasis.get(0).getContent() : "");
        infoVO.setSellingPointProduct(CollUtil.isNotEmpty(sellingPointProduct)? sellingPointProduct.get(0).getContent() : "");

        List<OrderVideoTag> orderVideoTags = Optional.ofNullable(orderVideoTagService.selectListByVideoIdAndCategory(id, ModelTagEnum.CATEGORY.getCode())).orElse(new ArrayList<>());
        infoVO.setProductCategory(orderVideoTags.stream().map(OrderVideoTag::getTagId).collect(Collectors.toList()));

        if (StrUtil.isNotBlank(infoVO.getReferencePicId())) {
            resourceIds.addAll(StringUtils.splitToLong(infoVO.getReferencePicId(), StrUtil.COMMA));
        }
        if (StrUtil.isNotBlank(orderVideo.getParticularEmphasisPicIds())) {
            resourceIds.addAll(StringUtils.splitToLong(orderVideo.getParticularEmphasisPicIds(), StrUtil.COMMA));
        }

        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);

        List<String> particularEmphasisPic = new ArrayList<>();
        if (StrUtil.isNotBlank(orderVideo.getParticularEmphasisPicIds())) {
            for (Long particularEmphasisPicId : StringUtils.splitToLong(orderVideo.getParticularEmphasisPicIds(), StrUtil.COMMA)) {
                particularEmphasisPic.add(resourceMap.getOrDefault(particularEmphasisPicId, new OrderResource()).getObjectKey());
            }
        }

        infoVO.setParticularEmphasisPic(particularEmphasisPic);

        if (CharSequenceUtil.isNotBlank(orderVideo.getCautionsPicId())) {
            List<Long> cautionsPicIds = StringUtils.splitToLong(orderVideo.getCautionsPicId(), StrUtil.COMMA);
            List<String> cautionsPics = new ArrayList<>();
            for (Long cautionsPicId : cautionsPicIds) {
                cautionsPics.add(resourceMap.getOrDefault(cautionsPicId, new OrderResource()).getObjectKey());
            }
            infoVO.setCautionsPics(cautionsPics);
        }
        if (StrUtil.isNotBlank(infoVO.getReferencePicId())) {
            List<Long> referencePicIds = StringUtils.splitToLong(infoVO.getReferencePicId(), StrUtil.COMMA);
            List<String> referencePic = new ArrayList<>();
            for (Long referencePicId : referencePicIds) {
                referencePic.add(resourceMap.getOrDefault(referencePicId, new OrderResource()).getObjectKey());
            }
            infoVO.setReferencePic(referencePic);
        }

        return infoVO;
    }

    @Override
    public List<String> selectOrderUserNicknameList(String name) {
        return baseMapper.selectOrderUserNicknameList(name);
    }

    /**
     * 审核订单_获取视频详细信息
     *
     * @param id 视频订单的主键
     */
    @Override
    public OrderOperationVideoVO getOperationOrderVideoInfo(Long id, OrderListDTO dto) {
        OrderVideoVO orderVideoInfo = getOrderVideoInfo(id);
        if (ObjectUtil.isNull(orderVideoInfo)) {
            return null;
        }
        // Assert.isTrue(OrderStatusEnum.UN_CONFIRM.getCode().equals(orderVideoInfo.getStatus()), () -> new Biz200Exception(2, "订单状态有变动，请刷新后重试~"));
        OrderOperationVideoVO orderOperationVideoVO = BeanUtil.copyProperties(orderVideoInfo, OrderOperationVideoVO.class);

        return assembleOperationOrderVideoInfo(orderOperationVideoVO, dto);
    }

    /**
     * 修改订单费用
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderVideoPrice(UpdateOrderVideoPriceDTO dto) {
        OrderVideo orderVideo = getById(dto.getVideoId());
        checkVideoStatus(orderVideo, OrderStatusEnum.UN_PAY);
        if (ObjectUtil.isNull(orderVideo.getPicCount())) {
            Assert.isTrue(ObjectUtil.isNull(dto.getPicPrice()) || BigDecimal.ZERO.compareTo(dto.getPicPrice()) == 0, "视频订单没有选配，图片费用不能更改");
        }
        IOrderService orderService = SpringUtils.getBean(IOrderService.class);
        Order order = orderService.getOrderByOrderNum(orderVideo.getOrderNum());
        Assert.isFalse(order.getIsDefaultExchangeRate(), "订单百度汇率异常，暂不允许修改视频订单费用！");

        OrderVideoBackModifyAmountRecords orderVideoBackModifyAmountRecords = new OrderVideoBackModifyAmountRecords();
        orderVideoBackModifyAmountRecords.setVideoId(dto.getVideoId());
        orderVideoBackModifyAmountRecords.setOldVideoPrice(orderVideo.getVideoPrice());
        orderVideoBackModifyAmountRecords.setNewVideoPrice(dto.getVideoPrice());
        orderVideoBackModifyAmountRecords.setOldPicPrice(orderVideo.getPicPrice());
        orderVideoBackModifyAmountRecords.setNewPicPrice(dto.getPicPrice());
        orderVideoBackModifyAmountRecords.setOldCommissionPaysTaxes(orderVideo.getCommissionPaysTaxes());
        orderVideoBackModifyAmountRecords.setNewCommissionPaysTaxes(dto.getCommissionPaysTaxes());
        orderVideoBackModifyAmountRecords.setOldExchangePrice(orderVideo.getExchangePrice());
        orderVideoBackModifyAmountRecords.setNewExchangePrice(dto.getExchangePrice());
        orderVideoBackModifyAmountRecords.setOldServicePrice(orderVideo.getServicePrice());
        orderVideoBackModifyAmountRecords.setNewServicePrice(dto.getServicePrice());
        orderVideoBackModifyAmountRecords.setCreateBy(SecurityUtils.getUsername());
        orderVideoBackModifyAmountRecords.setCreateId(SecurityUtils.getUserId());

        BeanUtil.copyProperties(dto, orderVideo);

        BigDecimal amountDollar = orderVideo.getVideoPrice().add(orderVideo.getPicPrice()).add(orderVideo.getCommissionPaysTaxes()).add(orderVideo.getExchangePrice()).add(orderVideo.getServicePrice());
        orderVideo.setAmount(amountDollar.multiply(order.getCurrentExchangeRate()).setScale(2, RoundingMode.DOWN));
        orderVideo.setAmountDollar(amountDollar);
        if (ObjectUtil.isNotNull(orderVideo.getVideoPromotionAmount()) && BigDecimal.ZERO.compareTo(orderVideo.getVideoPromotionAmount()) != 0) {
            orderVideo.setPayAmount(orderVideo.getAmount().subtract(orderVideo.getVideoPromotionAmount()));
            orderVideo.setPayAmountDollar(orderVideo.getPayAmount().divide(order.getCurrentExchangeRate(), 2, RoundingMode.DOWN));
        }


        updateById(orderVideo);

        List<OrderVideo> orderVideos = baseMapper.selectByOrderNum(orderVideo.getOrderNum());

        final BigDecimal orderAmount = orderVideos.stream().map(OrderVideo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        final BigDecimal orderAmountDollar = orderVideos.stream().map(OrderVideo::getAmountDollar).reduce(BigDecimal.ZERO, BigDecimal::add);
        order.setBackModifyAmount(orderAmount.subtract(order.getOrderAmount()));
        // BigDecimal orderFinalAmount = orderService.getOrderFinalAmount(order);
        order.setPayAmount(orderAmount);
        order.setPayAmountDollar(orderAmountDollar);
        orderVideoBackModifyAmountRecordsService.saveOrderVideoBackModifyAmountRecords(orderVideoBackModifyAmountRecords);
        SpringUtils.getBean(WeChatService.class).closeAllQrcode(orderVideo.getOrderNum());
        orderService.releasePayLock(List.of(order));

        StringBuilder sb = new StringBuilder();
        if (CompareUtil.compare(orderVideoBackModifyAmountRecords.getOldVideoPrice(), orderVideoBackModifyAmountRecords.getNewVideoPrice()) != 0) {
            sb.append("视频佣金为$").append(orderVideoBackModifyAmountRecords.getNewVideoPrice()).append(StrPool.COMMA);
        }
        if (CompareUtil.compare(orderVideoBackModifyAmountRecords.getOldPicPrice(), orderVideoBackModifyAmountRecords.getNewPicPrice()) != 0) {
            sb.append("照片佣金为$").append(orderVideoBackModifyAmountRecords.getNewPicPrice()).append(StrPool.COMMA);
        }
        if (CompareUtil.compare(orderVideoBackModifyAmountRecords.getOldCommissionPaysTaxes(), orderVideoBackModifyAmountRecords.getNewCommissionPaysTaxes()) != 0) {
            sb.append("佣金代缴税费为$").append(orderVideoBackModifyAmountRecords.getNewCommissionPaysTaxes()).append(StrPool.COMMA);
        }
        if (CompareUtil.compare(orderVideoBackModifyAmountRecords.getOldExchangePrice(), orderVideoBackModifyAmountRecords.getNewExchangePrice()) != 0) {
            sb.append("PayPal代付手续费为$").append(orderVideoBackModifyAmountRecords.getNewExchangePrice()).append(StrPool.COMMA);
        }
        if (CompareUtil.compare(orderVideoBackModifyAmountRecords.getOldServicePrice(), orderVideoBackModifyAmountRecords.getNewServicePrice()) != 0) {
            sb.append("蜗牛服务费为$").append(orderVideoBackModifyAmountRecords.getNewServicePrice()).append(StrPool.COMMA);
        }
        if (CharSequenceUtil.isNotBlank(sb.toString())) {
            Assert.isTrue(CharSequenceUtil.isNotBlank(dto.getRemark()), "更改了费用，备注不能为空");
            //  记录操作记录
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ALTERATION_COST.getEventName(),
                    OrderVideoOperateTypeEnum.ALTERATION_COST.getIsPublic(),
                    OrderVideoOperateDTO.builder()
                            .videoId(dto.getVideoId())
                            .eventContent(CharSequenceUtil.format(OrderVideoOperateTypeEnum.ALTERATION_COST.getEventContent(), sb.substring(0, sb.length() - 1)))
                            .build()
            );
        }
        if (CharSequenceUtil.isNotBlank(dto.getRemark())) {
            //  记录操作记录
            orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.ALTERATION_COST_REMARK.getEventName(),
                    OrderVideoOperateTypeEnum.ALTERATION_COST_REMARK.getIsPublic(),
                    OrderVideoOperateDTO.builder()
                            .videoId(dto.getVideoId())
                            .eventContent(CharSequenceUtil.format(OrderVideoOperateTypeEnum.ALTERATION_COST_REMARK.getEventContent(), dto.getRemark()))
                            .eventExecuteTime(DateUtil.offsetSecond(DateUtil.date(), 3))
                            .build()
            );
        }

    }

    /**
     * 运营端-订单各个状态统计
     */
    @Override
    public OrderStatusVO backOrderStatusCount() {
        Map<String, Integer> codeMap = OrderStatusEnum.getCodeMap();
        return baseMapper.backOrderStatusCount(codeMap);
    }

    /**
     * 商家端-订单各个状态统计
     */
    @Override
    public OrderStatusVO merchantOrderStatusCount() {
        Map<String, Integer> codeMap = OrderStatusEnum.getCodeMap();
        return baseMapper.merchantOrderStatusCount(SecurityUtils.getLoginBusinessUser().getBusinessAccountVO().getBusinessId(), codeMap);
    }

    @Override
    public WorkbenchVO getWorkbenchStatisticsVO(Integer workbenchRoleType) {
        Assert.isTrue(UserTypeConstants.MANAGER.equals(SecurityUtils.getLoginUserType()), "只有运营端才能获取工作台数据！");
        LoginUser loginUser = (LoginUser) SecurityUtils.getLoginUser();
        SysUser sysUser = loginUser.getSysUser();
        WorkbenchDTO dto = new WorkbenchDTO();
        dto.setUserId(sysUser.getUserId());
        if (SecurityUtils.isAdmin(sysUser.getUserId())) {
            Assert.notNull(workbenchRoleType, "工作台角色类型不能为空~");
            dto.setWorkbenchRoleType(workbenchRoleType);
        } else {
            dto.setWorkbenchRoleType(sysUser.getWorkbenchRoleType());
        }
        if (WorkbenchRoleTypeEnum.CHINESE_DEPARTMENT.getCode().equals(dto.getWorkbenchRoleType())) {
            OrderTaskStatisticsVO orderTaskStatistics = SpringUtils.getBean(OrderVideoTaskService.class).getOrderTaskStatistics(SecurityUtils.currentUserIsAdmin() ? null : SecurityUtils.getUserId(), null);
            WorkbenchVO workbenchStatisticsVO = Optional.ofNullable(baseMapper.getWorkbenchStatisticsVO(dto)).orElse(WorkbenchVO.builder().unConfirmCount(0).pauseMatchCount(0).build());
            BeanUtil.copyProperties(orderTaskStatistics, workbenchStatisticsVO);
            Integer logisticCount = getLogisticCount(Arrays.asList(LogisticMainStatus.NOT_FOUND.getCode(), LogisticMainStatus.DELIVERY_FAILURE.getCode(), LogisticMainStatus.EXCEPTION.getCode()), Arrays.asList(OrderStatusEnum.UN_FINISHED.getCode()));
            workbenchStatisticsVO.setLogisticAnomalyCount(Optional.ofNullable(logisticCount).orElse(0));
            return workbenchStatisticsVO;
        } else if (WorkbenchRoleTypeEnum.ENGLISH_DEPARTMENT.getCode().equals(dto.getWorkbenchRoleType())) {
            OrderTaskStatisticsVO orderTaskStatistics = SpringUtils.getBean(OrderVideoTaskService.class).getOrderTaskStatistics(SecurityUtils.currentUserIsAdmin() ? null : SecurityUtils.getUserId(), null);
            WorkbenchVO workbenchVO = BeanUtil.copyProperties(orderTaskStatistics, WorkbenchVO.class);
            Integer logisticCount = getLogisticCount(Arrays.stream(LogisticMainStatus.values()).filter(item -> !LogisticMainStatus.DELIVERED.getCode().equals(item.getCode())).map(LogisticMainStatus::getCode).collect(Collectors.toList()), Arrays.asList(OrderStatusEnum.UN_FINISHED.getCode()));
            workbenchVO.setUnReceivingCount(Optional.ofNullable(logisticCount).orElse(0));

            EnglishStatisticsDTO englishStatisticsDTO = new EnglishStatisticsDTO();
            if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
                List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
                if (CollUtil.isEmpty(modelPeople)) {
                    workbenchVO.setUnContactTotalCount(0);
                    workbenchVO.setUnContactCount(0);
                    workbenchVO.setContactingCount(0);
                    return workbenchVO;
                }
                englishStatisticsDTO.setModelIds(modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toSet()));
            }
            EnglishStatisticsVO englishStatisticsVO = orderVideoMatchPreselectModelService.selectEnglishWorkbenchStatistics(englishStatisticsDTO);
            BeanUtil.copyProperties(englishStatisticsVO, workbenchVO);
            return workbenchVO;

        } else if (WorkbenchRoleTypeEnum.FINANCE_DEPARTMENT.getCode().equals(dto.getWorkbenchRoleType())) {
            WorkbenchVO auditOrderStatistics = Optional.ofNullable(orderMapper.getAuditOrderStatistics()).orElse(WorkbenchVO.builder().videoUnAuditCount(0).memberUnAuditCount(0).build());
            BackInvoiceStatisticsVO statisticsVO = Optional.ofNullable(orderInvoiceMapper.backInvoiceStatistics()).orElse(new BackInvoiceStatisticsVO());
            auditOrderStatistics.setQuantityToBeInvoiced(statisticsVO.getQuantityToBeInvoiced());
            //填充提现待审批、分销待结算
            WorkbenchVO financeWorkbenchVo = Optional.ofNullable(remoteService.getFinanceWorkbenchVo()).orElse(WorkbenchVO.builder().payoutUnAuditCount(0L).distributionUnSettleCount(0L).build());
            auditOrderStatistics.setPayoutUnAuditCount(financeWorkbenchVo.getPayoutUnAuditCount());
            auditOrderStatistics.setDistributionUnSettleCount(financeWorkbenchVo.getDistributionUnSettleCount());
            return auditOrderStatistics;

        } else if (WorkbenchRoleTypeEnum.EDITING_DEPARTMENT.getCode().equals(dto.getWorkbenchRoleType())) {
            OrderTaskStatisticsVO orderTaskStatistics = SpringUtils.getBean(OrderVideoTaskService.class).getOrderTaskStatistics(null, SecurityUtils.currentUserIsAdmin() ? null : SecurityUtils.getUserId());
            WorkbenchVO workbenchStatistics = Optional.ofNullable(SpringUtils.getBean(IOrderVideoFeedBackMaterialInfoService.class).getWorkbenchStatistics()).orElse(WorkbenchVO.builder().downloadCount(0).toBeEditedCount(0).waitForFeedbackCount(0).build());
            BeanUtil.copyProperties(orderTaskStatistics, workbenchStatistics);
            Long havenTUploaded = orderVideoUploadLinkMapper.getHavenTUploaded();
            workbenchStatistics.setUnUploadLinkCount(Optional.ofNullable(havenTUploaded).orElse(0L));
            return workbenchStatistics;
        } else {
            return new WorkbenchVO();
        }
    }

    public Integer getLogisticCount(List<Integer> logisticMainStatusList, List<Integer> videoStatusList) {
        Collection<String> logisticNumbers = remoteService.getLogisticNumbersByCondition(null, logisticMainStatusList);
        List<OrderVideoModelShippingAddress> modelShippingAddresses = orderVideoModelShippingAddressService.selectModelShippingAddressByLogisticNumber(logisticNumbers);
        if (CollUtil.isEmpty(modelShippingAddresses)) {
            return 0;
        }
        LogisticCountDTO logisticCountdto = new LogisticCountDTO();
        logisticCountdto.setVideoIds(modelShippingAddresses.stream().map(OrderVideoModelShippingAddress::getVideoId).collect(Collectors.toSet()));
        logisticCountdto.setRollbackIds(modelShippingAddresses.stream().map(OrderVideoModelShippingAddress::getRollbackId).collect(Collectors.toSet()));
        logisticCountdto.setVideoStatus(videoStatusList);
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            logisticCountdto.setUserId(SecurityUtils.getUserId());
        }
        Integer logisticCount = baseMapper.getLogisticCount(logisticCountdto);
        if (ObjectUtil.isNull(logisticCount)) {
            return 0;
        }
        return logisticCount;
    }


    @Override
    public List<OrderVideoVO> chineseUnConfirmList() {
        List<OrderVideoVO> orderVideoVOS = baseMapper.selectChineseUnConfirmList(SecurityUtils.currentUserIsAdmin() ? null : SecurityUtils.getUserId());
        //加载意向模特
        if (CollUtil.isEmpty(orderVideoVOS)) {
            return Collections.emptyList();
        }
        List<Long> videoIds = orderVideoVOS.stream().map(OrderVideoVO::getId).collect(Collectors.toList());
        List<Long> rejectedModelVideoIds = orderVideoMatchPreselectModelService.getRejectedModelVideoIds(videoIds);

        List<Long> modelIds = orderVideoVOS.stream().map(OrderVideoVO::getIntentionModelId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, ModelOrderSimpleVO> modelMap = remoteService.getModelSimpleMap(modelIds);
        for (OrderVideoVO item : orderVideoVOS) {
            item.setIntentionModel(modelMap.get(item.getIntentionModelId()));
            item.setHasRejectedModel(rejectedModelVideoIds.contains(item.getId()));
        }
        // 加载意向模特
        return orderVideoVOS;
    }

    @Override
    public List<OrderVideoVO> englishUnMatchList() {
        List<Long> modelIds = new ArrayList<>();
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
            if (CollUtil.isEmpty(modelPeople)) {
                return Collections.emptyList();
            }
            modelIds.addAll(modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toSet()));
        }

        List<OrderVideoVO> orderVideoVOS = baseMapper.selectEnglishUnMatchList(modelIds, OrderVideoMatchStatusEnum.NORMAL.getCode());
        //加载意向模特
        if (CollUtil.isEmpty(orderVideoVOS)) {
            return Collections.emptyList();
        }
        List<Long> intentionModelIds = orderVideoVOS.stream().map(OrderVideoVO::getIntentionModelId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(intentionModelIds)) {
            Map<Long, ModelOrderSimpleVO> modelMap = remoteService.getModelSimpleMap(intentionModelIds);
            for (OrderVideoVO item : orderVideoVOS) {
                item.setIntentionModel(modelMap.get(item.getIntentionModelId()));
            }
        }
        //加载意向模特
        return orderVideoVOS;
    }

    @Override
    public List<OrderVideoVO> englishCloseList() {
        List<OrderVideoVO> orderVideoVOS = baseMapper.selectEnglishCloseList(SecurityUtils.currentUserIsAdmin()? null : SecurityUtils.getUserId());
        //加载拍摄模特
        if (CollUtil.isEmpty(orderVideoVOS)) {
            return Collections.emptyList();
        }
        List<Long> shootModelIds = orderVideoVOS.stream().map(OrderVideoVO::getShootModelId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(shootModelIds)) {
            Map<Long, ModelOrderSimpleVO> modelMap = remoteService.getModelSimpleMap(shootModelIds);
            for (OrderVideoVO item : orderVideoVOS) {
                item.setShootModel(modelMap.get(item.getShootModelId()));
            }
        }

        return orderVideoVOS;
    }

    @Override
    public List<PauseMatchVO> englishPauseMatchList() {
        List<Long> modelIds = new ArrayList<>();
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            List<ModelPerson> modelPeople = remoteService.selectCurrentUserRelevanceModel();
            if (CollUtil.isEmpty(modelPeople)) {
                return Collections.emptyList();
            }
            modelIds.addAll(modelPeople.stream().map(ModelPerson::getModelId).collect(Collectors.toSet()));
        }
        if (CollUtil.isEmpty(modelIds)) {
            return Collections.emptyList();
        }

        List<OrderVideoMatchPreselectModel> isPauseOust = orderVideoMatchPreselectModelService.findIsPauseOust(modelIds);
        List<Long> matchIds = isPauseOust.stream().map(OrderVideoMatchPreselectModel::getMatchId).collect(Collectors.toList());
        if (CollUtil.isEmpty(matchIds)) {
            return Collections.emptyList();
        }

        // 查询所有匹配的数据并转换为 Map
        Map<Long, OrderVideoMatch> matchMap = orderVideoMatchService.list(
                new LambdaQueryWrapper<OrderVideoMatch>().in(OrderVideoMatch::getId, matchIds)
                                                                         ).stream().collect(Collectors.toMap(OrderVideoMatch::getId, Function.identity()));

        // 按 matchIds 的顺序构建 allMatches
        List<OrderVideoMatch> allMatches = matchIds.stream()
                                                   .map(matchMap::get)
                                                   .filter(Objects::nonNull)
                                                   .collect(Collectors.toList());

        List<Long> videoIds = allMatches.stream().map(OrderVideoMatch::getVideoId).collect(Collectors.toList());

        List<OrderVideoMatch> orderVideoMatchList = orderVideoMatchService.list(new LambdaQueryWrapper<OrderVideoMatch>().in(OrderVideoMatch::getVideoId, videoIds));
        List<OrderVideoMatch> videoMatchList = orderVideoMatchList.stream()
                                                                  .collect(Collectors.groupingBy(OrderVideoMatch::getVideoId))
                                                                  .values()
                                                                  .stream()
                                                                  .map(group -> group.stream().max(Comparator.comparing(OrderVideoMatch::getId)).orElse(null))
                                                                  .filter(match -> match != null && OrderVideoMatchStatusEnum.PAUSE.getCode().equals(match.getStatus()))
                                                                  .collect(Collectors.toList());

        if (CollectionUtil.isEmpty(videoMatchList)) {
            return Collections.emptyList();
        }

        // 按照videoIds的顺序，取出videoMatchList中的5条数据，生成新的list
        Map<Long, OrderVideoMatch> videoMatchMap = videoMatchList.stream()
                .collect(Collectors.toMap(OrderVideoMatch::getVideoId, Function.identity(), (existing, replacement) -> existing));

        List<OrderVideoMatch> orderedVideoMatchList = videoIds.stream()
                .map(videoMatchMap::get)
                .filter(Objects::nonNull)
                .limit(5)
                .collect(Collectors.toList());

        List<Long> list = orderedVideoMatchList.stream().map(OrderVideoMatch::getVideoId).collect(Collectors.toList());
        List<OrderVideo> list1 = this.list(new LambdaQueryWrapper<OrderVideo>().in(OrderVideo::getId, list));
        ArrayList<PauseMatchVO> pauseMatchVOS = new ArrayList<>();
        for (OrderVideo orderVideo : list1) {
            pauseMatchVOS.add(PauseMatchVO.builder()
                    .videoCode(orderVideo.getVideoCode())
                    .productChinese(orderVideo.getProductChinese())
                    .productEnglish(orderVideo.getProductEnglish())
                    .pauseReason(videoMatchMap.get(orderVideo.getId()).getPauseReason())
                    .build());
        }
        // baseMapper.selectEnglishPauseMatchList(modelIds);
        return pauseMatchVOS;
    }

    @Override
    public OrderStatusVO workbenchOrderStatusCount() {
        BusinessAccountVO businessAccountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
        Map<String, Integer> codeMap = OrderStatusEnum.getCodeMap();

        return baseMapper.workbenchOrderStatusCount(businessAccountVO.getBusinessId()
                , StatusTypeEnum.NO.getCode().equals(businessAccountVO.getIsOwnerAccount())? businessAccountVO.getAccount(): null
                , codeMap);
    }

    /**
     * 更换模特
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CreateOrderVO changeModel(Long videoId, Long newModelId, String reason) {
        OrderVideo orderVideo = getById(videoId);
        checkVideoStatus(orderVideo, OrderStatusEnum.NEED_FILLED);

        try {
            Assert.isTrue(redisService.getLock(CacheConstants.ORDER_FLOW_KEY + orderVideo.getId(), 60L), "订单流转中，请稍后重试！");
            //  校验模特可否接单
            Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(Collections.singletonList(newModelId));
            if (ObjectUtil.isNotNull(newModelId)) {
                Collection<Long> cannotModelIds = SpringUtils.getBean(IOrderService.class).checkModelCanAccept(Collections.singletonList(newModelId), SecurityUtils.getBizUserId());
                if (CollUtil.isNotEmpty(cannotModelIds)) {
                    return CreateOrderVO.builder().cannotModel(cannotModelIds).build();
                }
                orderVideoMatchPreselectModelService.checkModelConformOrderVideoInfo(modelMap, orderVideo);
            }

            //  新增视频订单的预选模特
            // orderVideoPreselectModelService.saveVideoPreselect(videoId, newModelId);

            //  将原来的预选模特设置为淘汰
            orderVideoMatchService.changeModel(videoId, reason);

            //  通过视频订单ID删除模特关联订单
            orderVideoModelService.deleteOrderVideoModelByVideoId(videoId);

            orderVideo.setIntentionModelId(newModelId);
            baseMapper.changeModel(orderVideo);

            SpringUtils.getBean(OrderVideoLogisticFollowService.class).deleteByVideoIds(Arrays.asList(videoId));

            //  删除模特收件地址
            orderVideoModelShippingAddressService.removeOrderVideoModelShippingAddressByVideoIdAndRollbackId(orderVideo.getId(), orderVideo.getRollbackId());

            orderVideoFlowRecordService.createOrderVideoFlow("更换模特", List.of(OrderVideoFlowDTO.builder().videoId(orderVideo.getId()).originStatus(OrderStatusEnum.NEED_FILLED.getCode()).targetStatus(OrderStatusEnum.UN_CONFIRM.getCode()).build()));
            // SpringUtils.getBean(IOrderService.class).createOrderFlow(orderVideo, OrderStatusEnum.UN_MATCH, "更换模特");

            //  清除标记订单
            // ClearFlagOrder clearFlagOrder = new ClearFlagOrder();
            // clearFlagOrder.setVideoId(videoId);
            // clearFlagOrder.setModelId(orderVideo.getShootModelId());
            // SpringUtils.getAopProxy(this).clearFlagOrder(Collections.singletonList(clearFlagOrder));

            if (ObjectUtil.isNotNull(newModelId)) {
                orderVideoOperateService.createOrderVideoOperate(
                        OrderVideoOperateTypeEnum.CHANGE_MODEL.getEventName(),
                        null,
                        OrderVideoOperateTypeEnum.CHANGE_MODEL.getIsPublic(),
                        null,
                        OrderVideoOperateDTO
                                .builder()
                                .videoId(videoId)
                                .eventContent(StrUtil.format(OrderVideoOperateTypeEnum.CHANGE_MODEL.getEventContent(), Optional.ofNullable(modelMap.get(newModelId)).orElse(new ModelInfoVO()).getName()))
                                .eventContentCompany(StrUtil.format(OrderVideoOperateTypeEnum.CHANGE_MODEL.getEventContentCompany(), Optional.ofNullable(modelMap.get(newModelId)).orElse(new ModelInfoVO()).getName()))
                                .build()
                );
            } else {
                orderVideoOperateService.createOrderVideoOperate(
                        OrderVideoOperateTypeEnum.CHANGE_MODEL_NO_CHOICE.getEventName(),
                        null,
                        OrderVideoOperateTypeEnum.CHANGE_MODEL_NO_CHOICE.getIsPublic(),
                        null,
                        OrderVideoOperateDTO
                                .builder()
                                .videoId(videoId)
                                .eventContent(OrderVideoOperateTypeEnum.CHANGE_MODEL_NO_CHOICE.getEventContent())
                                .eventContentCompany(OrderVideoOperateTypeEnum.CHANGE_MODEL_NO_CHOICE.getEventContentCompany())
                                .build());
            }

            orderVideoFlowNodeDiagramService.setNodeCompleteTimeNull(videoId, OrderVideoFlowNodeEnum.MATCHING_MODEL);

            if (ObjectUtil.isNotNull(newModelId)) {
                orderVideoModelChangeService.saveOrderVideoModelChange(OrderVideoModelChangeDTO.builder().videoId(videoId).rollbackId(orderVideo.getRollbackId()).modelId(newModelId).source(OrderVideoModelChangeSourceEnum.INTENTION_MODEL.getCode()).build());
            }
            return new CreateOrderVO();

        } finally {
            redisService.releaseLock(CacheConstants.ORDER_FLOW_KEY + orderVideo.getId());
        }
    }

    /**
     * 当
     * 1、选定的模特无法接单
     * 2、更改了拍摄模特
     * 3、更改了选定模特
     * 时 清除视频订单的[标记订单]的相关字段 以及 清除 视频订单的携带订单相关
     */
    @Override
    public void clearFlagOrder(List<ClearFlagOrder> clearFlagOrders) {
        List<Long> videoIds = clearFlagOrders.stream().map(ClearFlagOrder::getVideoId).collect(Collectors.toList());
        baseMapper.clearFlagOrder(videoIds);
    }

    /**
     * 查询订单_视频列表
     *
     * @param orderListDTO 订单_视频
     * @return 订单_视频集合
     */
    @Override
    public List<OrderVideoVO> selectOrderVideoListByCondition(OrderListDTO orderListDTO) {
        List<OrderVideoVO> orderVideoVOS = baseMapper.selectOrderVideoListByCondition(orderListDTO);
        if (CollUtil.isNotEmpty(orderVideoVOS)) {
            orderVideoVOS.forEach(item->{

                if (ObjectUtil.isNotNull(item.getPicCount())){
                    item.setSurplusPicCount(PicCountEnum.getValue(item.getPicCount()) - Optional.ofNullable(item.getRefundPicCount()).orElse(0));
                }else {
                    item.setSurplusPicCount(0);
                }
            });
        }

        return orderVideoVOS;
    }

    @Override
    public List<OrderVideoVO> selectAccountOrderList() {
        AccountOrderListDTO dto = new AccountOrderListDTO();
        Assert.isTrue(SecurityUtils.getLoginUserType().equals(UserTypeConstants.USER_TYPE), "用户类型错误");
        BusinessAccountVO businessAccountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
        dto.setMerchantId(businessAccountVO.getBusinessId());
        if (StatusTypeEnum.NO.getCode().equals(businessAccountVO.getIsOwnerAccount())) {
            dto.setCreateOrderUserAccount(businessAccountVO.getAccount());
        }
        List<OrderVideoVO> orderVideoVOS = baseMapper.selectAccountOrderList(dto);
        if (CollUtil.isEmpty(orderVideoVOS)){
            return Collections.emptyList();
        }
        List<Long> modelId = orderVideoVOS.stream().map(OrderVideoVO::getShootModelId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, ModelOrderSimpleVO> modelSimpleMap = remoteService.getModelSimpleMap(modelId);
        for (OrderVideoVO item : orderVideoVOS){
            item.setShootModel(modelSimpleMap.get(item.getShootModelId()));
        }
        return orderVideoVOS;
    }

    /**
     * 创建视频订单
     *
     * @param orderVideoDTOS 视频订单
     * @param orderNum       订单编号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createOrderVideo(List<OrderVideoDTO> orderVideoDTOS, String orderNum) {
        BusinessAccountVO accountVO = SecurityUtils.getLoginBusinessUser().getBusinessAccountVO();
        Long contactId = accountVO.getBusinessVO().getWaiterId();

        //  视频订单关联内容
        List<OrderVideoContent> orderVideoContents = new ArrayList<>();
        //  需要爬图的视频订单
        List<AsyncCrawlProductPicDTO> asyncCrawlProductPicDTOS = new ArrayList<>();
        List<OrderVideoCurrentInfoVO> orderVideoCurrentInfoVOS = new ArrayList<>();
        List<Long> orderVideoIds = new ArrayList<>();
        List<UpdateCartToOrderVideoOperateDTO> updateCartToOrderVideoOperateDTOS = new ArrayList<>();
        //  视频订单操作记录
        List<OrderVideoOperateDTO> orderVideoOperateDTOS = new ArrayList<>();
        //  视频订单使用优惠详情
        List<OrderPromotionDetail> orderPromotionDetails = new ArrayList<>();
        DateTime date = DateUtil.date();

        Map<Integer, List<OrderVideoDTO>> orderVideoDTOMap = orderVideoDTOS.stream().collect(Collectors.groupingBy(OrderVideoDTO::getSerialNumber));
        orderVideoDTOMap.forEach((key, value) -> {
            String referencePic = null;
            boolean isCart = ObjectUtil.isNotNull(value.get(0).getVideoCartId());
            if (!isCart) {
                List<Long> resourceIds = orderResourceService.saveBatchOrderResource(value.get(0).getReferencePic());
                referencePic = StrUtil.join(StrUtil.COMMA, resourceIds);
            }
            for (int i = 0; i < value.size(); i++) {
                OrderVideoDTO videoDTO = value.get(i);
                OrderVideo orderVideo = BeanUtil.copyProperties(videoDTO, OrderVideo.class);
                if (!isCart) {
                    if (CollUtil.isNotEmpty(videoDTO.getIntentionModelIds()) && i < videoDTO.getIntentionModelIds().size()) {
                        orderVideo.setIntentionModelId(videoDTO.getIntentionModelIds().get(i));
                    }
                    orderVideo.setCreateOrderBusinessId(accountVO.getBusinessId());
                    orderVideo.setCreateOrderBusinessProxyStatus(accountVO.getBusinessVO().getIsProxy());
                    orderVideo.setCreateOrderBizUserId(accountVO.getBizUserId());
                    orderVideo.setCreateOrderUserId(accountVO.getId());
                    orderVideo.setCreateOrderUserAccount(accountVO.getAccount());
                    orderVideo.setCreateOrderUserName(accountVO.getName());
                    orderVideo.setCreateOrderUserNickName(accountVO.getNickName());
                    orderVideo.setCreateOrderOperationUserName(accountVO.getName());
                    orderVideo.setCreateOrderOperationUserNickName(accountVO.getNickName());
                }
                orderVideo.setVideoStyle(orderVideo.getPlatform());
                if (PlatformEnum.ELSE.getCode().equals(orderVideo.getPlatform())){
                    orderVideo.setVideoStyle(VideoStyleEnum.AMAZON.getCode());
                }else if (PlatformEnum.APP.getCode().equals(orderVideo.getPlatform())){
                    orderVideo.setVideoStyle(VideoStyleEnum.APP.getCode());
                }

                orderVideo.setOrderNum(orderNum);
                orderVideo.setContactId(contactId);
                orderVideo.setStatus(OrderStatusEnum.UN_PAY.getCode());
                orderVideo.setStatusTime(date);
                orderVideo.setIsObject(getIsObjectByPlatform(orderVideo.getPlatform()));
                if (!isCart) {
                    orderVideo.setReferencePicId(StrUtil.isBlank(referencePic) ? null : referencePic);
                }
                if (CharSequenceUtil.isBlank(videoDTO.getProductPic())) {
                    orderVideo.setProductPic(videoDTO.getCrawlProductPic());
                }
                baseMapper.insert(orderVideo);

                orderVideoOperateDTOS.add(OrderVideoOperateDTO.builder().videoId(orderVideo.getId()).eventContent(OrderVideoOperateTypeEnum.CREATE_ORDER.getEventContent()).build());
                orderVideoIds.add(orderVideo.getId());

                if (StrUtil.isBlank(orderVideo.getProductPic()) && StrUtil.isNotBlank(orderVideo.getProductLink())) {
                    AsyncCrawlProductPicDTO dto = new AsyncCrawlProductPicDTO();
                    dto.setId(orderVideo.getId());
                    dto.setProductLink(orderVideo.getProductLink());
                    asyncCrawlProductPicDTOS.add(dto);
                }
                videoDTO.setId(orderVideo.getId());
                videoDTO.initOrderVideoContents();
                orderVideoContents.addAll(videoDTO.getOrderVideoContents());
                videoDTO.getOrderVideoContents().clear();

                OrderVideoCurrentInfoVO orderVideoCurrentInfoVO = BeanUtil.copyProperties(videoDTO, OrderVideoCurrentInfoVO.class);
                orderVideoCurrentInfoVO.setVideoStyle(orderVideo.getVideoStyle());
                orderVideoCurrentInfoVO.setIntentionModelId(orderVideo.getIntentionModelId());
                orderVideoCurrentInfoVOS.add(orderVideoCurrentInfoVO);

                for (OrderPromotionDetail orderPromotionDetail : videoDTO.getOrderPromotionDetails()) {
                    orderPromotionDetail.setOrderNum(orderVideo.getOrderNum());
                    orderPromotionDetail.setVideoId(orderVideo.getId());
                    orderPromotionDetails.add(orderPromotionDetail);
                }
            }

            if (!isCart) {
                updateCartToOrderVideoOperateDTOS.add(UpdateCartToOrderVideoOperateDTO.builder().cartId(value.get(0).getVideoCartId()).videoId(value.get(0).getId()).build());
            }
        });

        orderPromotionDetailService.saveBatchOrderPromotionDetail(orderPromotionDetails);

        //  新增视频关联内容
        if (CollUtil.isNotEmpty(orderVideoContents)) {
            videoContentService.saveBatchVideoContent(orderVideoContents.stream().map(OrderVideoContent::getVideoId).collect(Collectors.toSet()), null, orderVideoContents, StatusTypeEnum.YES.getCode());
        }

        //  需要爬图的视频订单
        if (CollUtil.isNotEmpty(asyncCrawlProductPicDTOS)) {
            AsyncCrawlTask asyncCrawlTask = new AsyncCrawlTask();
            asyncCrawlTask.setType(OrderTypeEnum.VIDEO_ORDER.getCode());
            asyncCrawlTask.setAsyncCrawlProductPic(asyncCrawlProductPicDTOS);
            SpringUtils.getBean(IOrderService.class).crawlTask(asyncCrawlTask);
        }
        asyncTaskService.addVideoInitChangeLog(orderVideoCurrentInfoVOS);

        List<OrderVideoFlowDTO> orderVideoFlowDTOS = orderVideoIds.stream().map(videoId -> {
            OrderVideoFlowDTO orderVideoFlowDTO = new OrderVideoFlowDTO();
            orderVideoFlowDTO.setVideoId(videoId);
            orderVideoFlowDTO.setTargetStatus(OrderStatusEnum.UN_PAY.getCode());
            return orderVideoFlowDTO;
        }).collect(Collectors.toList());
        orderVideoFlowRecordService.createOrderVideoFlow("创建订单", orderVideoFlowDTOS);

        if (CollUtil.isNotEmpty(updateCartToOrderVideoOperateDTOS)) {
            orderVideoOperateService.updateCartToOrderVideoOperate(updateCartToOrderVideoOperateDTOS);
        }

        orderVideoOperateService.createOrderVideoOperate(OrderVideoOperateTypeEnum.CREATE_ORDER.getEventName(), null, OrderVideoOperateTypeEnum.CREATE_ORDER.getIsPublic(), null, orderVideoOperateDTOS);
        orderVideoFlowNodeDiagramService.initOrderVideoFlowNodeDiagram(orderVideoIds);
    }

    /**
     * 修改订单_视频
     *
     * @param orderVideoDTO 订单_视频
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateOrderVideo(OrderVideoDTO orderVideoDTO) {
        orderVideoDTO.formatVideoLink();
        orderVideoDTO.setPicCount(null);
        OrderVideo orderVideo = getById(orderVideoDTO.getId());
        Assert.notNull(orderVideo, "找不到订单，请刷新后重试");
        Order order = SpringUtils.getBean(IOrderService.class).getOrderByOrderNum(orderVideo.getOrderNum());
        Assert.notNull(order, "找不到订单，请刷新后重试");
        Assert.isNull(order.getCloseOrderTime(), "订单已取消，无法修改订单信息");
        Assert.isNull(order.getPayTime(), "已支付，无法修改订单信息");
        Assert.isNull(order.getSubmitCredentialTime(), "已支付，无法修改订单信息");
        if (orderVideo.getVideoPromotionAmount().compareTo(BigDecimal.ZERO) > 0) {
            Assert.isTrue(orderVideo.getPlatform().compareTo(orderVideoDTO.getPlatform()) == 0, "使用平台不能改变");
            Assert.isTrue(StrUtil.equals(orderVideo.getProductLink(), orderVideoDTO.getProductLink()), "产品链接不能改变");
            orderVideoDTO.setProductPic(null);
            Assert.isTrue(orderVideo.getShootingCountry().compareTo(orderVideoDTO.getShootingCountry()) == 0, "拍摄国家不能改变");
        }

        boolean isChangeProductLink = !StrUtil.equals(orderVideo.getProductLink(), orderVideoDTO.getProductLink());
        if (isChangeProductLink) {
            if (StringUtils.isNotBlank(orderVideoDTO.getProductLink())) {
                AsyncCrawlProductPicDTO asyncCrawlProductPicDTO = new AsyncCrawlProductPicDTO();
                asyncCrawlProductPicDTO.setId(orderVideo.getId());
                asyncCrawlProductPicDTO.setProductLink(orderVideoDTO.getProductLink());

                AsyncCrawlTask asyncCrawlTask = new AsyncCrawlTask();
                asyncCrawlTask.setType(OrderTypeEnum.VIDEO_ORDER.getCode());
                asyncCrawlTask.setAsyncCrawlProductPic(Collections.singletonList(asyncCrawlProductPicDTO));

                SpringUtils.getBean(IOrderService.class).crawlTask(asyncCrawlTask);
            }
        }
        BeanUtil.copyProperties(orderVideoDTO, orderVideo, copyOptions);
        orderVideo.setReferenceVideoLink(orderVideoDTO.getReferenceVideoLink());
        orderVideo.setIntentionModelId(orderVideoDTO.getIntentionModelId());
        if (isChangeProductLink && StringUtils.isBlank(orderVideo.getProductPic()) && StringUtils.isNotBlank(orderVideo.getProductLink())) {
            orderVideo.setProductPic(null);
        }
        orderVideo.setIsObject(getIsObjectByPlatform(orderVideo.getPlatform()));
        if (CollUtil.isNotEmpty(orderVideoDTO.getReferencePic())) {
            List<Long> resourceIds = orderResourceService.saveBatchOrderResource(orderVideoDTO.getReferencePic());
            orderVideo.setReferencePicId(StrUtil.join(StrUtil.COMMA, resourceIds));
        } else {
            orderVideo.setReferencePicId(null);
        }
        baseMapper.updateOrderVideo(orderVideo);

        orderVideoDTO.initOrderVideoContents();
        videoContentService.saveBatchVideoContent(List.of(orderVideoDTO.getId()), List.of(VideoContentTypeEnum.REQUIRE.getCode(), VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode()), orderVideoDTO.getOrderVideoContents(), StatusTypeEnum.NO.getCode());
    }

    /**
     * 组装运营查看视频订单的回显数据
     */
    private OrderOperationVideoVO assembleOperationOrderVideoInfo(OrderOperationVideoVO orderOperationVideoVO, OrderListDTO dto) {
        List<Long> refundVideoIds = SpringUtils.getBean(IOrderVideoRefundService.class).selectVideoIdByRefund(null);
        Assert.isFalse(refundVideoIds.contains(orderOperationVideoVO.getId()), () -> new Biz200Exception(2, "订单有退款申请或已退款，请刷新后重试~"));

        List<Long> rejectedModelVideoIds = orderVideoMatchPreselectModelService.getRejectedModelVideoIds(List.of(orderOperationVideoVO.getId()));
        orderOperationVideoVO.setHasRejectedModel(rejectedModelVideoIds.contains(orderOperationVideoVO.getId()));

        Long userId = SecurityUtils.getUserId();
        if (!SecurityUtils.isAdmin(userId)) {
            if (CollUtil.isNotEmpty(dto.getContactId()) && !dto.getContactId().contains(userId)) {
                return orderOperationVideoVO;
            }
            dto.getContactId().clear();
            dto.getContactId().add(userId);
        }

        if (CollUtil.isNotEmpty(dto.getStatus()) && !dto.getStatus().contains(OrderStatusEnum.UN_CONFIRM.getCode())) {
            return orderOperationVideoVO;
        }
        dto.getStatus().clear();
        dto.getStatus().add(OrderStatusEnum.UN_CONFIRM.getCode());

        dto.setRefundVideoIds(refundVideoIds);

        if (CharSequenceUtil.isNotBlank(dto.getStatusTimeSort())) {
            dto.setStatusTimeSort(OrderByDto.DIRECTION.DESC.value().equals(dto.getStatusTimeSort()) ? OrderByDto.DIRECTION.ASC.value() : OrderByDto.DIRECTION.DESC.value());
        }

        List<OrderVideoTag> orderVideoTags = orderVideoTagService.selectListByVideoIdAndCategory(orderOperationVideoVO.getId(), ModelTagEnum.CATEGORY.getCode());
        if (CollUtil.isNotEmpty(orderVideoTags)) {
            Set<Long> tagIds = orderVideoTags.stream().map(OrderVideoTag::getTagId).collect(Collectors.toSet());
            List<TagVO> tagVO = remoteService.getTagVO(tagIds);
            orderOperationVideoVO.setProductCategory(tagVO);
        }

        List<OrderListVO> orderListVOS = orderMapper.selectOrderListByCondition(dto, OrderTypeEnum.VIDEO_ORDER.getCode());
        if (CollUtil.isEmpty(orderListVOS)) {
            return orderOperationVideoVO;
        }

        Set<String> orderNums = orderListVOS.stream().map(OrderListVO::getOrderNum).collect(Collectors.toSet());
        dto.setOrderNums(orderNums);
        List<OrderVideoVO> orderVideoVOS = selectOrderVideoListByCondition(dto);

        List<OrderVideoVO> orderVideos = new ArrayList<>();
        // 组装 OrderListVO
        Map<String, List<OrderVideoVO>> orderMap = orderVideoVOS.stream().collect(Collectors.groupingBy(OrderVideoVO::getOrderNum));
        for (OrderListVO orderListVO : orderListVOS) {
            List<OrderVideoVO> videoVOS = orderMap.get(orderListVO.getOrderNum());
            if (CollUtil.isNotEmpty(videoVOS)) {
                orderVideos.addAll(videoVOS);
            }
        }


        for (int i = 0; i < orderVideos.size(); i++) {
            if (orderVideos.get(i).getId().equals(orderOperationVideoVO.getId())) {
                if (i > 0) {
                    orderOperationVideoVO.setPrevVideoId(orderVideos.get(i - 1).getId());
                }
                if (i < orderVideos.size() - 1) {
                    orderOperationVideoVO.setNextVideoId(orderVideos.get(i + 1).getId());
                }
                break;
            }
        }

        return orderOperationVideoVO;
    }


    /**
     * 组装商家端视频订单详情回显数据
     *
     * @param orderVideoVO 视频订单返回VO
     */
    private void assembleCompanyOrderVideoInfo(OrderVideoVO orderVideoVO) {
        assembleOrderVideoInfo(orderVideoVO);

        List<OrderVideoContent> orderVideoContents = videoContentService.selectListByVideoIdOrTypes(orderVideoVO.getId(), List.of(VideoContentTypeEnum.REQUIRE.getCode(), VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode()));

        List<OrderVideoContent> shootRequired = orderVideoContents.stream().filter(item -> VideoContentTypeEnum.REQUIRE.getCode().equals(item.getType())).collect(Collectors.toList());
        OrderVideoContent sellingPointProduct = orderVideoContents.stream().filter(item -> VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode().equals(item.getType())).findFirst().orElse(new OrderVideoContent());
        orderVideoVO.setShootRequired(shootRequired);
        orderVideoVO.setSellingPointProduct(sellingPointProduct.getContent());
    }

    /**
     * 组装视频订单详情回显数据
     *
     * @param orderVideoVO 视频订单返回VO
     */
    private void assembleOrderVideoInfo(OrderVideoVO orderVideoVO) {
        //  获取模特列表
        List<Long> modelIds = new ArrayList<>();
        modelIds.add(orderVideoVO.getIntentionModelId());
        modelIds.add(orderVideoVO.getShootModelId());

        Map<Long, ModelOrderSimpleVO> modelMap = remoteService.getModelSimpleMap(modelIds);
        orderVideoVO.setIntentionModel(modelMap.get(orderVideoVO.getIntentionModelId()));
        orderVideoVO.setShootModel(modelMap.get(orderVideoVO.getShootModelId()));

        //  获取关联的资源
        List<Long> referencePicIds = StringUtils.splitToLong(orderVideoVO.getReferencePicId(), StrUtil.COMMA);
        List<Long> resourceIds = new ArrayList<>(referencePicIds);
        List<Long> shippingPicIds = StringUtils.splitToLong(orderVideoVO.getShippingPic(), StrUtil.COMMA);
        resourceIds.addAll(shippingPicIds);

        Map<Long, OrderResource> resourceMap = orderResourceService.getResourceMapByIds(resourceIds);
        if (CollUtil.isNotEmpty(resourceMap)) {
            for (Long resourceId : referencePicIds) {
                orderVideoVO.getReferencePic().add(resourceMap.getOrDefault(resourceId, new OrderResource()).getObjectKey());
            }
            for (Long shippingPic : shippingPicIds) {
                orderVideoVO.getShippingPics().add(resourceMap.getOrDefault(shippingPic, new OrderResource()).getObjectKey());
            }
        }
    }

    /**
     * 获取需不需要发货
     */
    private Integer getIsObjectByPlatform(Integer platform) {
        if (PlatformEnum.APP.getCode().equals(platform)) {
            return IsObjectEnum.NO_OBJECT.getCode();
        } else {
            return IsObjectEnum.OBJECT.getCode();
        }
    }

    private void supplementaryData(OrderVideoCurrentInfoVO orderVideoCurrentInfo, OrderVideoCurrentInfoVO orderVideoLastInfo) {
        if (ObjectUtil.notEqual(orderVideoCurrentInfo.getIntentionModelId(), orderVideoLastInfo.getIntentionModelId())) {
            Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(CollUtil.toList(orderVideoCurrentInfo.getIntentionModelId(), orderVideoLastInfo.getIntentionModelId()));
            orderVideoCurrentInfo.setIntentionModelName(modelMap.getOrDefault(orderVideoCurrentInfo.getIntentionModelId(), new ModelInfoVO()).getName());
            orderVideoLastInfo.setIntentionModelName(modelMap.getOrDefault(orderVideoLastInfo.getIntentionModelId(), new ModelInfoVO()).getName());
        }
        if (CollUtil.isEmpty(orderVideoLastInfo.getParticularEmphasisPic())){
            orderVideoLastInfo.setParticularEmphasisPic(new ArrayList<>());
        }

        if (CollUtil.isNotEmpty(orderVideoLastInfo.getProductCategory())){
            orderVideoLastInfo.setProductCategory(orderVideoLastInfo.getProductCategory().stream().sorted().collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(orderVideoCurrentInfo.getProductCategory())){
            orderVideoCurrentInfo.setProductCategory(orderVideoCurrentInfo.getProductCategory().stream().sorted().collect(Collectors.toList()));
        }
        orderVideoLastInfo.setReferencePicId(null);
        orderVideoCurrentInfo.setReferencePicId(null);

        if (CollUtil.isNotEmpty(orderVideoCurrentInfo.getCautions()) && CollUtil.isNotEmpty(orderVideoLastInfo.getCautions())){
            orderVideoCurrentInfo.getCautions().forEach(item->{
                item.setId(0L);
                item.setSort(0);
                item.setVideoId(orderVideoCurrentInfo.getId());
                item.setFirstContent(null);
                item.setFirstEdit(null);
            });
            orderVideoLastInfo.getCautions().forEach(item->{
                item.setId(0L);
                item.setSort(0);
                item.setVideoId(orderVideoCurrentInfo.getId());
                item.setFirstContent(null);
                item.setFirstEdit(null);
            });
        }
        if (CollUtil.isNotEmpty(orderVideoCurrentInfo.getShootRequired()) && CollUtil.isNotEmpty(orderVideoLastInfo.getShootRequired())){
            orderVideoCurrentInfo.getShootRequired().forEach(item->{
                item.setId(0L);
                item.setSort(0);
                item.setVideoId(orderVideoCurrentInfo.getId());
                item.setFirstContent(null);
                item.setFirstEdit(null);
            });
            orderVideoLastInfo.getShootRequired().forEach(item->{
                item.setId(0L);
                item.setSort(0);
                item.setVideoId(orderVideoCurrentInfo.getId());
                item.setFirstContent(null);
                item.setFirstEdit(null);
            });
        }
    }

    /**
     * 比较模特收件信息
     *
     * @return true=有变更
     */
    public static Boolean compareModelShippingAddress(ModelInfoVO modelInfoVO, String recipient, String detailAddress, String city, String state, String zipcode, Integer nation, String phone) {
        if (!StrUtil.equals(recipient, modelInfoVO.getRecipient())) {
            return true;
        }
        if (!StrUtil.equals(detailAddress, modelInfoVO.getDetailAddress())) {
            return true;
        }
        if (!StrUtil.equals(city, modelInfoVO.getCity())) {
            return true;
        }
        if (!StrUtil.equals(state, modelInfoVO.getState())) {
            return true;
        }
        if (!StrUtil.equals(zipcode, modelInfoVO.getZipcode())) {
            return true;
        }
        if (!ObjectUtil.equals(nation, modelInfoVO.getNation())) {
            return true;
        }
//        if (!ObjectUtil.equals(phone, modelInfoVO.getPhone())) {
//            return true;
//        }
        return false;
    }

    @Override
    public void backUserEditOrderVideo(OrderOperationVideoDTO orderOperationVideoDTO, OrderVideo orderVideo, ChangeLogTypeEnum changeLog) {
        Assert.notNull(orderOperationVideoDTO, "订单信息不能为空");
        Assert.notNull(orderOperationVideoDTO.getVideoStyle(), "视频风格不能为空");
        orderOperationVideoDTO.formatVideoLink();;
        OrderVideoCurrentInfoVO orderVideoCurrentInfo = getOrderVideoCurrentInfo(orderOperationVideoDTO.getId());
        boolean isChangeProductLink = !StrUtil.equals(orderVideo.getProductLink(), orderOperationVideoDTO.getProductLink());
        if (isChangeProductLink) {
            AsyncCrawlProductPicDTO dto = new AsyncCrawlProductPicDTO();
            dto.setId(orderOperationVideoDTO.getId());
            dto.setProductLink(orderOperationVideoDTO.getProductLink());

            AsyncCrawlTask asyncCrawlTask = new AsyncCrawlTask();
            asyncCrawlTask.setType(OrderTypeEnum.VIDEO_ORDER.getCode());
            asyncCrawlTask.setAsyncCrawlProductPic(CollUtil.toList(dto));
            SpringUtils.getBean(IOrderService.class).crawlTask(asyncCrawlTask);
        }
        if (ObjectUtil.isNotNull(orderOperationVideoDTO.getIntentionModelId())) {
            Map<Long, ModelInfoVO> modelMap = remoteService.getModelMap(List.of(orderOperationVideoDTO.getIntentionModelId()));
            orderVideoMatchPreselectModelService.checkModelConformOrderVideoInfo(modelMap, BeanUtil.copyProperties(orderOperationVideoDTO, OrderVideo.class));
            if (ObjectUtil.notEqual(orderOperationVideoDTO.getIntentionModelId(), orderVideo.getIntentionModelId())) {
                orderVideoModelChangeService.saveOrderVideoModelChange(OrderVideoModelChangeDTO.builder()
                        .videoId(orderOperationVideoDTO.getId())
                        .rollbackId(orderVideo.getRollbackId())
                        .modelId(orderOperationVideoDTO.getIntentionModelId())
                        .source(OrderVideoModelChangeSourceEnum.INTENTION_MODEL.getCode())
                        .build());
            }
        }

        orderOperationVideoDTO.initOrderVideoContents();
        Collection<Integer> types = new ArrayList<>(List.of(
                VideoContentTypeEnum.REQUIRE.getCode(),
                VideoContentTypeEnum.CAUTIONS.getCode(),
                VideoContentTypeEnum.ORDER_SPECIFICATION_REQUIRE.getCode(),
                VideoContentTypeEnum.PARTICULAR_EMPHASIS.getCode(),
                VideoContentTypeEnum.PARTICULAR_EMPHASIS_PIC.getCode(),
                VideoContentTypeEnum.SELLING_POINT_PRODUCT.getCode(),
                VideoContentTypeEnum.CLIPS_REQUIRED.getCode()
        ));
        if (orderOperationVideoDTO.getAuditType() != null) {
            types.add(orderOperationVideoDTO.getAuditType());
        }
        videoContentService.saveBatchVideoContent(List.of(orderOperationVideoDTO.getId()), types, orderOperationVideoDTO.getOrderVideoContents(), ChangeLogTypeEnum.AUDIT_EDIT_LOG.equals(changeLog) ? StatusTypeEnum.YES.getCode() : StatusTypeEnum.NO.getCode());

        List<OrderVideoTag> orderVideoTags = orderOperationVideoDTO.getProductCategory().stream().map(tagId -> {
            OrderVideoTag orderVideoTag = new OrderVideoTag();
            orderVideoTag.setVideoId(orderOperationVideoDTO.getId());
            orderVideoTag.setTagId(tagId);
            orderVideoTag.setCategoryId(ModelTagEnum.CATEGORY.getCode());
            return orderVideoTag;
        }).collect(Collectors.toList());
        if (Arrays.asList(OrderStatusEnum.NEED_FILLED.getCode(), OrderStatusEnum.UN_FINISHED.getCode()).contains(orderVideo.getStatus())){
            List<OrderVideoTag> orderVideoTagTable = Optional.ofNullable(orderVideoTagService.selectListByVideoIdAndCategory(orderVideo.getId(), ModelTagEnum.CATEGORY.getCode())).orElse(new ArrayList<>());
            List<Long> tagIds = orderVideoTagTable.stream().map(OrderVideoTag::getTagId).collect(Collectors.toList());
            //差集为空
            Assert.isTrue(CollUtil.disjunction(tagIds, orderOperationVideoDTO.getProductCategory()).isEmpty(), "产品分品类不能修改");
        }
        orderVideoTagService.saveBatchOrderVideoTag(orderVideoTags);

        orderOperationVideoDTO.setPicCount(null);
        BeanUtil.copyProperties(orderOperationVideoDTO, orderVideo);
        if (isChangeProductLink && StringUtils.isBlank(orderVideo.getProductPic()) && StringUtils.isNotBlank(orderVideo.getProductLink())) {
            orderVideo.setProductPic("");
        }
        orderVideo.setIsObject(getIsObjectByPlatform(orderVideo.getPlatform()));

        if (CollUtil.isNotEmpty(orderOperationVideoDTO.getReferencePic())) {
            List<Long> resourceIds = orderResourceService.saveBatchOrderResource(orderOperationVideoDTO.getReferencePic());
            String referencePic = StrUtil.join(StrUtil.COMMA, resourceIds);
            orderVideo.setReferencePicId(StrUtil.isBlank(referencePic) ? null : referencePic);
        } else {
            orderVideo.setReferencePicId(null);
        }
        if (CollUtil.isNotEmpty(orderOperationVideoDTO.getParticularEmphasisPic())){
            List<Long> resourceIds = orderResourceService.saveBatchOrderResource(orderOperationVideoDTO.getParticularEmphasisPic());
            String referencePic = StrUtil.join(StrUtil.COMMA, resourceIds);
            orderVideo.setParticularEmphasisPicIds(StrUtil.isBlank(referencePic) ? null : referencePic);
        }else {
            orderVideo.setParticularEmphasisPicIds(null);
        }

        orderVideo.setLastChangeTime(DateUtil.date());
        if (ObjectUtil.isNotNull(orderOperationVideoDTO.getOrderVideoCautionsDTO()) && CollUtil.isNotEmpty(orderOperationVideoDTO.getOrderVideoCautionsDTO().getCautionsPics())) {
            List<Long> resourceIds = orderResourceService.saveBatchOrderResource(orderOperationVideoDTO.getOrderVideoCautionsDTO().getCautionsPics());
            orderVideo.setCautionsPicId(StrUtil.join(StrUtil.COMMA, resourceIds));
        } else {
            orderVideo.setCautionsPicId(null);
        }
        baseMapper.updateOrderVideo(orderVideo);

        OrderVideoCurrentInfoVO orderVideoLastInfo = BeanUtil.copyProperties(orderOperationVideoDTO, OrderVideoCurrentInfoVO.class);
        if (CollUtil.isNotEmpty(orderOperationVideoDTO.getOrderVideoContents())) {
            List<VideoContentDTO> videoContentDTOS = new ArrayList<>();
            for (OrderVideoContent item : orderOperationVideoDTO.getOrderVideoContents()) {
                if (VideoContentTypeEnum.REQUIRE.getCode().equals(item.getType())) {
                    videoContentDTOS.add(BeanUtil.copyProperties(item, VideoContentDTO.class));
                }
            }
            orderVideoLastInfo.setShootRequired(videoContentDTOS);
        }
        if (ObjectUtil.isNotNull(orderOperationVideoDTO.getOrderVideoCautionsDTO())) {
            orderVideoLastInfo.setCautions(orderOperationVideoDTO.getOrderVideoCautionsDTO().getCautions());
            orderVideoLastInfo.setCautionsPics(orderOperationVideoDTO.getOrderVideoCautionsDTO().getCautionsPics());
        }
        supplementaryData(orderVideoCurrentInfo, orderVideoLastInfo);
        try {
            Map<String, Object> differencesMap = ObjectUtils.compareAndReturnDifferences(orderVideoCurrentInfo, orderVideoLastInfo);
            if (ObjectUtil.isNotNull(differencesMap.get("particularEmphasisPic")) && ObjectUtil.isNull(differencesMap.get("particularEmphasis"))){
                differencesMap.put("particularEmphasis", orderVideoLastInfo.getParticularEmphasis());
            }
            if (ObjectUtil.isNull(differencesMap.get("particularEmphasisPic")) && ObjectUtil.isNotNull(differencesMap.get("particularEmphasis"))){
                differencesMap.put("particularEmphasisPic", orderVideoLastInfo.getParticularEmphasisPic());
            }

            if (differencesMap.containsKey("productCategory")){
                if (CollUtil.isNotEmpty(orderVideoLastInfo.getProductCategory())) {
                    List<TagVO> tagVO = remoteService.getTagVO(orderVideoLastInfo.getProductCategory());
                    if (CollUtil.isNotEmpty(tagVO)) {
                        orderVideoLastInfo.setProductCategoryName(StrUtil.join(StrPool.COMMA, tagVO.stream().map(TagVO::getName).collect(Collectors.toList())));
                    }
                }
                differencesMap.put("productCategoryName", orderVideoLastInfo.getProductCategoryName());
            }
            if (CollUtil.isEmpty(differencesMap)) {
                differencesMap.put("isNothing", Boolean.TRUE);
            }
            OrderVideoChangeLogDTO orderVideoChangeLogDTO = OrderVideoChangeLogDTO.builder().videoId(orderOperationVideoDTO.getId()).rollbackId(orderVideo.getRollbackId()).logType(changeLog.getCode()).data(differencesMap).build();
            orderVideoChangeLogService.addVideoChangeLog(orderVideoChangeLogDTO);
        } catch (Exception e) {
            log.error(RECORD_ORDER_INFORMATION_CHANGE_EXCEPTION, e);
            throw new ServiceException(RECORD_ORDER_INFORMATION_CHANGE_EXCEPTION);
        }
    }

    @Override
    public Date getOrderFirstMatchTime(Long orderId) {
        return baseMapper.getOrderFirstMatchTime(orderId);
    }

    /**
     * 获取同链接产品有回退订单的历史拍摄模特ID
     */
    @Override
    public List<Long> selectSameProductRollbackModelIdsByVideoId(Long videoId) {
        OrderVideo orderVideo = baseMapper.selectById(videoId);
        Assert.notNull(orderVideo, "订单视频不存在");
        if (CharSequenceUtil.isBlank(orderVideo.getProductLink())) {
            return Collections.emptyList();
        }
        return baseMapper.selectSameProductRollbackModelIdsByVideoId(orderVideo.getProductLink());
    }
}
