from sdk.task import Task, Executor, GlueType, RouteStrategy, ExecutorBlockStrategy, ScheduleType, \
    MisfireStrategy

if __name__ == '__main__':
    with Executor(app_name='order-job') as executor:
        cleanupInvalidPreselectModelsHandler = Task(
            title='清理无效预选模特数据',
            author='WangYimin',
            executor_handler='cleanupInvalidPreselectModels',
            glue_type=GlueType.BEAN,
            schedule_type=ScheduleType.CRON,
            schedule_conf='0 */5 * * * ?',
            executor_param='',
            executor_route_strategy=RouteStrategy.ROUND,
            misfire_strategy=MisfireStrategy.DO_NOTHING,
            executor_block_strategy=ExecutorBlockStrategy.SERIAL_EXECUTION,
            online=True
        )
        
        executor.submit(cleanupInvalidPreselectModelsHandler)
