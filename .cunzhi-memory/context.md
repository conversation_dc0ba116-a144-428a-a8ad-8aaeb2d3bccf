# 项目上下文信息

- Model实体类isShow字段控制模特在前端的展示状态，当设置为0时模特会从所有公开列表中隐藏，但不影响已有订单和收藏关系，主要通过ModelServiceImpl.updateModel方法处理，会记录变更日志到ModelChangeRecord表
- 用户要求重写远程服务中的 getModelsForPreselectCleanup 方法，该方法在 OrderVideoMatchPreselectModelServiceImpl 的 getStatusInvalidPreselectModels 方法中被调用，用于查询状态异常和不展示的预选模特
- 跨数据库查询问题：user_model_blacklist表在biz数据库，order_video_match_preselect_model表在order数据库，不能直接JOIN查询。解决方案是通过Feign服务间调用获取黑名单数据，在应用层进行数据匹配和过滤。
- 用户询问了Spring Boot测试方法是否能注册到Nacos的问题，确认了使用@SpringBootTest和@ActiveProfiles("test")的集成测试会启动完整Spring上下文并注册到Nacos，可以进行真实的OpenFeign调用
- 在 order-business/db/changelog/changelog-wym-1.0.yml 中添加了changeSet wym-order-1，使用setColumnRemarks修改表order_video_match_preselect_model字段distribution_result_cause的注释，新增了9：模特已下架，10：模特有逾期订单，11：商家拉黑模特三个选项
